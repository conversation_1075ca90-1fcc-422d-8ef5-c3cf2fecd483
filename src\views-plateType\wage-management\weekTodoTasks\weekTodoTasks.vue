<template>
  <!-- 待办任务-->
  <content-panel>
    <template v-slot:search>
      <search-box class="search-box">
        <el-form size="mini" :inline="true" :model="searchForm" ref="searchForm">
          <el-form-item label="核算工厂:" prop="factoryId">
            <el-select v-model="searchForm.factoryId" filterable clearable placeholder="请选择工厂" @change="onSearch">
              <el-option v-for="item in tabList" :key="item.id" :label="item.label" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="核算月份:" prop="accountingMonth">
            <el-date-picker :picker-options="pickerOptions" v-model="searchForm.accountingMonth" type="month"
              placeholder="请选择日期" @change="onSearch">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="核算周期:" prop="accountingWeek">
            <el-select v-model="searchForm.accountingWeek" filterable clearable placeholder="请选择周期" @change="onSearch">
              <el-option v-for="item in weeksList" :key="item.weekNumber" :label="'第' + item.weekNumber + '周'"
                :value="item.weekNumber">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="任务阶段:" prop="stage">
            <el-select v-model="searchForm.stage" filterable clearable placeholder="请选择任务状态" @change="onSearch">
              <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button size="small" type="primary" @click="onSearch">
            查询</el-button>
          <el-button size="small" type="warning" @click="resetSearchForm">
            重置</el-button>
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <template v-slot:header-left>
        <el-tabs v-model="activeTab">
          <el-tab-pane name="0" label="当前任务"> </el-tab-pane>
          <el-tab-pane name="1" label="历史任务"> </el-tab-pane>
        </el-tabs>
      </template>
      <el-table stripe border v-loading="loading" ref="tableRef" highlight-current-row :data="tableData"
        :height="maxTableHeight" style="width: 100%">
        <el-table-column prop="factoryName" label="核算工厂" width="130" align="left" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="accountingMonth" label="核算月份" width="80" align="left">
        </el-table-column>
        <el-table-column prop="accountingWeek" label="核算周期" width="120" align="left" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <div v-show="row.accountingWeek">
              {{ '第' + row.accountingWeek + '周' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="nodeName" label="任务阶段" width="120" align="left" show-overflow-tooltip>
        </el-table-column>
        <el-table-column label="任务状态" width="80" align="left">
          <template slot-scope="{ row }">
            {{
              row.completeState != null && row.completeState == 0
                ? "进行中"
                : "已完成" || ""
            }}
          </template>
        </el-table-column>
        <el-table-column prop="salaryId" label="周工资表" align="left" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <el-button type="text" v-permission="'was-customized$pieceworkWage$plateType$weekTodoTasks$weekPayroll'"
              v-if="row.salaryId" @click="handleJump(row)" class="active">
              {{ row.salaryId }}
            </el-button>
            <span v-else>/</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="left" :width="activeTab == '0' ? 500 : 150">
          <template slot-scope="scope">
            <el-button v-permission="'was-customized$pieceworkWage$plateType$weekTodoTasks$viewTasks'
              " type="text" size="mini" @click="handleLook(scope.$index, scope.row)">
              查看任务</el-button>
            <el-button v-permission="'was-customized$pieceworkWage$plateType$weekTodoTasks$endCollection'
              " type="text" size="mini" v-show="scope.row.nodeName == '资料收集' && activeTab == '0'"
              @click="endCollection(scope.row)">
              结束收集</el-button>
          </template>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <div style="text-align: right">
          <el-pagination @size-change="onSizeChange" @current-change="onNumChange" :current-page="pageNum"
            :page-size="pageSize" :total="total" :page-sizes="[50, 100, 200]"
            layout="total, sizes, prev, pager, next, jumper">
          </el-pagination>
        </div>
      </template>
    </table-panel>
    <task-dialog v-if="visible" :visible="visible" :title="title" :taskInfo="taskInfo" @cancel="handleCancel" />
  </content-panel>
</template>
<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import moment from "moment";
import taskDialog from "./taskDialog";
export default {
  name: "PlateTypeWeekTodoTasks",
  mixins: [tableMixin, pagePathMixin],
  components: { taskDialog },
  data() {
    return {
      searchForm: {
        accountingMonth: '',
        stage: "",
        factoryId: "",
        accountingWeek: '',
      },
      activeTab: "0",
      tabList: [],
      visible: false,
      title: "",
      statusOptions: [
        {
          label: '资料收集',
          value: 10
        },
        {
          label: '任务结束',
          value: 60
        }
      ],
      filterParam: {
        accountingMonth: '',
        accountingWeek: '',
      },
      weeksList: [],
      tableData: [],
      loading: false,
      pageSize: 50,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      pageNum: 1,
      total: 0,
      taskInfo: {},
      startTime: 0,
      diffTime: 0,
    };
  },
  created() {
    this.$api.plateTypeSystemManage.getBasicPermission
      .getBasicPermissionAll()
      .then(({ data }) => {
        this.tabList =
          data.map((item) => ({
            label: item.name,
            name: item.name,
            id: item.id,
          })) || [];
      });
  },
  watch: {
    $route: {
      handler(value) {
        if (value.path.includes("weekTodoTasks") && value.path.includes("plateType")) {
          this.weeksList = this.getCurrentMonthWeeks();
          this.filterParam.accountingWeek = this.getCurrentWeek();
          this.searchForm.accountingWeek = this.getCurrentWeek();
          this.getList(this.activeTab);
        }
      },
      // 深度观察监听
      deep: true,
      immediate: true,
    },
    activeTab: {
      handler(value) {
        this.weeksList = this.getCurrentMonthWeeks();
        this.filterParam.accountingWeek = this.getCurrentWeek();
        this.searchForm.accountingWeek = this.getCurrentWeek();
        this.getList(value);
      },
      immediate: true,
    },
  },
  methods: {
    getCurrentMonthWeeks(customDate) {
      const targetDate = customDate || (this.searchForm && this.searchForm.accountingMonth) || moment();

      const momentDate = moment.isMoment(targetDate) ? targetDate : moment(targetDate);

      const firstDay = momentDate.clone().startOf('month');
      const endDay = momentDate.clone().endOf('month');

      let weekStart = firstDay.clone().startOf('isoWeek');
      const weeks = [];
      while (weekStart.isSameOrBefore(endDay)) {
        weeks.push({
          weekNumber: weeks.length + 1,
          start: weekStart.format('YYYY-MM-DD'),
          end: weekStart.clone().endOf('isoWeek').format('YYYY-MM-DD')
        });
        weekStart.add(7, 'days');
      }
      return weeks;
    },
    getCurrentWeek(customDate) {
      const targetDate = customDate || moment().subtract(1, 'week');
      const targetMonth = moment(targetDate).format('YYYY-MM');
      this.searchForm.accountingMonth = targetMonth;
      this.filterParam.accountingMonth = targetMonth;
      const weeks = this.getCurrentMonthWeeks(targetDate);
      for (const week of weeks) {
        if (moment(targetDate).isBetween(week.start, week.end, null, '[]')) {
          return week.weekNumber;
        }
      }
      return 1;
    },
    //获取任务总览列表
    getList(complete = "0") {
      this.loading = true;
      this.$api.plateTypePieceWageSystem.weekTodoTasks
        .getWeekTodoTasksList({
          pageSize: this.pageSize,
          pageNum: this.pageNum,
          filterData: { ...this.filterParam, complete, moduleId: 3 },
        })
        .then(({ data: { list, total } }) => {
          this.tableData = list || [];
          this.total = total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.filterParam = {
        accountingMonth: moment().format("YYYY-MM"),
      };
      this.weeksList = this.getCurrentMonthWeeks();
      this.filterParam.accountingWeek = this.getCurrentWeek();
      this.searchForm.accountingWeek = this.getCurrentWeek();
      this.pageNum = 1;
      this.getList(this.activeTab);
    },
    //搜索
    onSearch() {
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (key == "accountingMonth" && moment.isDate(val)) {
          this.filterParam.accountingMonth = moment(val).format("YYYY-MM");
        } else if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      this.pageNum = 1;
      this.getList(this.activeTab);
    },
    focusEvent(name, data) {
      if (Array.isArray(data) && !data.length) {
        this.params[name] = [];
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.params[name] = data;
      }
    },
    //周工资表 跳转
    handleJump(data) {
      const { id, factoryId, factoryName, accountingWeek, accountingMonth, completeState } = data;
      let params = {
        id,
        factoryId,
        factoryName,
        accountingMonth,
        name: `${moment(accountingMonth).format("YYYY年MM月")}第${accountingWeek}周${factoryName}工资表`,
        completeState,
        accountingWeek
      };
      this.openSubPage({
        path: "/plateType/pieceworkWage/weeklyPayroll",
        query: {
          data: JSON.stringify(params),
        },
      });
    },
    //结束收集
    async endCollection(row) {
      this.title = "结束收集";
      this.visible = true;
      this.taskInfo = { ...row };
    },
    //查看任务
    handleLook(_, data) {
      this.openSubPage({
        path: "/plateType/pieceworkWage/weeklyTaskList",
        query: {
          data: this.$Base64.encode(
            JSON.stringify({
              factoryId: data.factoryId,
              accountingMonth: data.accountingMonth,
              accountingWeek: data.accountingWeek,
              activeTab: this.activeTab
            }),
          ),
        },
      });
    },
    handleCancel(type) {
      this.visible = false;
      if (type == "cancel") return;
      this.getList();
    },
    onSizeChange(val) {
      this.pageSize = val;
      this.pageNum = 1;
      this.getList(this.activeTab);
    },
    onNumChange(val) {
      this.pageNum = val;
      this.getList(this.activeTab);
    },
  },
};
</script>

<style lang="stylus" scoped>
#item {
  margin: 0;
  padding: 5px;
}

.active {
  width: 100%;
  padding: 0;

  >>>span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
    width: 100%;
    text-align: left;
  }
}

.formData {
  .el-form-item {
    display: flex;

    >>>.el-form-item__content {
      width: 100%;
      margin-left: 0 !important;
    }
  }
}
</style>
