<template>
  <div :class="{
    'has-logo':showLogo,
    'is-collapsed': isCollapse
  }">
    <logo v-if="showLogo" :collapse="isCollapse"/>
    
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <template v-for="item of menus">
        <!-- 当前应用节点，不可点击与悬浮 -->
        <div v-if="item.id === 'curApp'"
          :key="item.id" 
          class="cur-app-menu ellipis">
          <font-icon v-if="isCollapse"  :icon='item.icon'/>
          <span v-else>{{item.name}}</span>
        </div>
        <div v-else :key="item.id" 
          class="menu-item" 
          :class="{
            active: currentMenu == item.id,
            'my-app-menu': item.id === 'myApp'
          }"
          @mouseenter="e => changeMenu(item, e)">
          <div class="menu-item-link" v-if="item.children && item.children.length">
            <font-icon :icon='item.icon'/>
            <span v-if="!isCollapse">{{item.name}}</span>
          </div>
          <router-link v-else :to="item.path || ''" class="menu-item-link">
            <font-icon :icon='item.icon'/>
            <span v-if="!isCollapse" class="ellipis">{{item.name}}</span>
          </router-link>
        </div>
      </template>
    </el-scrollbar>

    <!-- 所有菜单共用一个pop -->
    <el-popover
      v-model="isShowPop"
      popper-class="sub-menu-pop"
      ref="subMenuPop"
      placement="right"
      width="375"
      :open-delay="100"
      :close-delay="0">
      <div class="float-menu-box">
        <div @mouseleave="hideMenuPopManually">
          <template v-for="subItem of subMenus">
            <div class="float-menu-group-title" :key="subItem.id">
              {{subItem.name}}
            </div>
            <div v-if="subItem.children"
              :key="'group' + subItem.id" 
              class="float-menu-group-content" 
              :class="{
                'float-menu-my-app': currentItem && currentItem.id === 'myApp'
              }">
              <div class="float-menu-item" v-for="childItem of subItem.children" :key="childItem.id">
                <el-link 
                  v-if="childItem.path && childItem.path.includes('http')" 
                  :href="childItem.path"
                  :underline="false">
                  {{childItem.name}}
                </el-link>
                <router-link v-else :to="childItem.path || ''" @click.native="hideMenuPopDelayed">
                  <span>{{childItem.name}}</span>
                </router-link>
              </div>
            </div>
          </template>
        </div>
      </div>
    </el-popover>
    <!-- 单独控制遮罩，避免闪烁现象 -->
    <transition name="fade">
      <sub-menu-mask v-if="isShowPopMask" :isCollapsed="isCollapse" @click.native="hideMenuPopManually"/>
    </transition>
  </div>
</template>
<script>
  import Logo from './logo';
  import {mapGetters} from 'vuex'
  import SubMenuMask from './sub-menu-mask'
  export default {
    components: {
      Logo,
      SubMenuMask
    },
    data() {
      return {
        showLogo: true,
        currentItem: null,
        subMenus: [],
        isShowPop: false,
        isShowPopMask: false
      }
    },
    computed: {
      ...mapGetters([
        'menus',
        'sidebar',
        'currentMenu'
      ]),
      isCollapse() {
        return !this.sidebar.opened
      }
    },
    methods: {
      changeMenu(data, e) {
        if (this.currentItem !== data) {
          this.subMenus = data.children ? data.children : [];
          if (this.subMenus.length) {
            this.showMenuPop(e);
          } else {
            this.hideMenuPop();
          }
        }
        this.currentItem = data;
        this.$store.dispatch('menu/updateCurrentMenu', data.id);
      },
      showMenuPop(e) {
        const menuPop = this.$refs.subMenuPop;
        if (!menuPop) {
          return;
        }
        // 此时不关闭遮罩，避免闪烁
        this.isShowPop = false;
        menuPop.doDestroy(true);
        this.$nextTick(() => {
          menuPop.referenceElm = menuPop.$refs.reference = e.target;
          this.isShowPop = true;
          this.isShowPopMask = true;
        });
      },
      hideMenuPop() {
        this.isShowPop = false;
        this.isShowPopMask = false;
      },
      hideMenuPopManually() {
        this.currentItem = null;
        this.hideMenuPop();
      },
      hideMenuPopDelayed() {
        setTimeout(() => {
          this.hideMenuPopManually();
        }, 200);
      }
    }
  }
</script>

<style lang="stylus" scoped>
.ellipis
  overflow hidden
  white-space nowrap
  text-overflow ellipsis
.menu-item
  overflow hidden
  padding 15px 10px
  white-space nowrap
  color #fff
  cursor pointer
  outline 0 solid #fff
  font-size 14px
  &:hover
    background rgba(255,255,255,0.2)
    color #ffffff
  &.active
    background #fff
    color #0aa580 
    a
      color #0aa580
      text-decoration none
    .menu-item-link
      color #0aa580
  &-link
    display flex
    align-items center
    color #ffffff
    >i
      width 18px
      font-size 16px
    >span
      flex 1
      margin-left 10px
      width 100%;
  &.my-app-menu
    border-bottom 1px solid rgba(255,255,255,0.5)
.cur-app-menu
  padding 15px 10px
  display flex
  color #fff
  background-color #08a580
  // background-image: linear-gradient(to bottom, rgba(19,209,183,0.5), rgba(8,165,128,0.5), rgba(19,209,183,0.5));
  font-size 16px

// 折叠部分样式
.is-collapsed
  .cur-app-menu,
  .menu-item-link,
  .my-app-menu
    justify-content center
  
</style>
<style lang="stylus">
.float-menu-box
  padding 10px 15px
  background-color #fff
  border-radius 3px
  overflow-y auto
  max-height calc(100vh - 40px)
  font-size 12px
  &::-webkit-scrollbar
    width 10px
    height 10px
    background-color #f4f4f4

  &::-webkit-scrollbar-track
    -webkit-box-shadow inset 0 0 6px #f1f1f1
    border-radius 10px
    background-color #ccc

  &::-webkit-scrollbar-thumb
    border-radius 10px
    -webkit-box-shadow inset 0 0 6px #999
    background-color #999

  .float-menu-group-title
    padding 5px 0
    color #0aa580
    font-weight bold
    &:not(:first-of-type)
      margin-top 15px
  .float-menu-group-content
    padding-left 1em
    &.float-menu-my-app a
      font-size 14px
  .float-menu-item
    display inline-block
    width 150px
    cursor pointer
    overflow hidden
    white-space nowrap
    text-overflow ellipsis
    vertical-align bottom
    border-radius: 3px
    &:nth-of-type(odd)
      margin-right 15px
    a
      display: inline-block
      padding 5px 3px
      width 100%
      font-size 12px
      color #666666
      text-decoration none
      &:hover
        color #0aa580
        background-color: #f4f4f4
    
.sub-menu-pop
  padding 0 !important
  z-index 9999 !important
</style>
