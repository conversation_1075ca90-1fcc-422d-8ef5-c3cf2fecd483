<template>
  <content-panel>
    <template v-slot:search>
      <search-box class="search-box">
        <el-form
          size="mini"
          :inline="true"
          :model="searchForm"
          ref="searchForm"
          class="rangeTime"
          label-width="90px"
          label-position="right"
        >
          <el-form-item label="员工姓名:" prop="staffName">
            <el-input
              clearable
              v-model.trim="searchForm.staffName"
              size="mini"
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffNames', $event)"
                  @focusEvent="focusEvent('staffNames', $event)"
                  ref="childrenStaffNames"
                  titleName="员工姓名"
                />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="厂牌编号:" prop="staffCode">
            <el-input
              v-model.trim="searchForm.staffCode"
              size="mini"
              clearable
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffCodes', $event)"
                  @focusEvent="focusEvent('staffCodes', $event)"
                  ref="childrenStaffCodes"
                  titleName="厂牌编号"
                />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="工厂名称:" prop="factoryId">
            <el-select
              v-model="searchForm.factoryId"
              filterable
              clearable
              placeholder="请选择工厂名称"
              @change="onSearch"
            >
              <el-option
                v-for="item in tabList"
                :key="item.value"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item class="range" label="核算月份:" prop="accountingMonth">
            <el-date-picker :clearable="false" v-model="searchForm.startYearMonth" value-format="yyyy-MM" type="month" placeholder="开始月份" clearable>
            </el-date-picker>
            <span class="separator">至</span>
            <el-date-picker :clearable="false" v-model="searchForm.endYearMonth" value-format="yyyy-MM" type="month" placeholder="结束月份" clearable>
            </el-date-picker>
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button
            size="small"
            type="primary"

            @click="onSearch"
          >
            查询
          </el-button>
          <el-button size="small" type="warning"  @click="resetSearchForm">
            重置
          </el-button>
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <template v-slot:header-right>
        <el-button size="small" type="primary" @click="handleExport">
          导出
        </el-button>
      </template>
      <el-table
        stripe
        border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :height="maxTableHeight"
        :data="tableData"
      >
        <el-table-column prop="staffName" label="员工姓名" min-width fixed>
        </el-table-column>
        <el-table-column prop="staffCode" label="厂牌编号" min-width fixed>
        </el-table-column>
        <el-table-column prop="factoryName" label="工厂名称" min-width fixed>
        </el-table-column>
        <el-table-column
          prop="accountingMonth"
          label="核算月份"
          min-width
          fixed
        >
        </el-table-column>
        <el-table-column prop="groupName" label="核算班组" min-width fixed>
        </el-table-column>
        <el-table-column prop="deductAmount" label="扣款金额" min-width fixed>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <div style="text-align: right">
          <el-pagination
            @size-change="onSizeChange"
            @current-change="onNumChange"
            :current-page="pageNum"
            :page-size="pageSize"
            :total="total"
            :page-sizes="[50, 100, 200]"
            layout="total, sizes, prev, pager, next, jumper"
          >
          </el-pagination>
        </div>
      </template>
    </table-panel>
  </content-panel>
</template>

<script>
import moment from "moment";
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import { calculateTableWidth } from "@/utils";
import { moneyFormat } from "@/utils";
export default {
  name: "reworkdeDuction",
  mixins: [tableMixin,pagePathMixin],
  data() {
    return {
      searchForm: {
        staffCode:"",
        staffName:"",
        factoryId: "",
        accountingMonth:"",
        startYearMonth:"",
        endYearMonth:""
      },

      tabList: [],
      filterParam: {},
      params: {},
      tableData: [],
      loading: false,
      pageSize: 50,
      pageNum: 1,
      total: 0,

    };
  },
  async created() {
    this.$api.systemManage.getBasicPermission
      .getBasicPermissionAll()
      .then((res) => {
        this.tabList = res.data.map((item) => ({
          label: item.name,
          name: item.name,
          id: item.id,
        }));
      });
    this.getList();
  },
  watch: {
    $route: {
      handler(value) {
        if (
          value.path.includes("salarySearch") &&
          value.path.includes("customized")
        ) {
          this.getList();
        }
      },
      deep: true,
    },
  },
  methods: {
    getList() {
      this.loading = true;
      this.$api.reportManagement.reworkFromlist({
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        filterData: {
          ...this.filterParam,
        },
      }).then((res) => {
        this.tableData = res.data.list
        this.total = res.data.total
      }).finally(() => {
          this.loading = false;
        });
    },

    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.$refs.childrenStaffNames.resetFilter();
      this.$refs.childrenStaffCodes.resetFilter();
      this.filterParam = {};
      this.searchForm.startYearMonth = ''
      this.searchForm.endYearMonth = ''
      this.params = {};
      this.onSearch();
    },
    //搜索
    onSearch(name, data) {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        // if (key == "accountingMonth") {
        //   if (moment.isDate(val)) {
        //     this.filterParam.accountingMonth = moment(val).format("YYYY-MM");
        //   }
        // }
        if(key == 'startYearMonth' && val){
            this.filterParam.startYearMonth = moment(val).format('YYYY-MM') || '';
          }else if(key == 'endYearMonth' && val){
            this.filterParam.endYearMonth = moment(val).format('YYYY-MM') || '';
          }else if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0)
          this.filterParam[name] = data;
      }
      this.pageNum = 1;
      this.getList();
    },

    focusEvent(name, data) {
      if (Array.isArray(data) && !data.length) {
        this.params[name] = [];
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.params[name] = data;
      }
    },
    //计算表格列宽度
    flexWidth(...args) {
      return calculateTableWidth(...args);
    },
   //导出
   handleExport() {
      let params = {
        ...this.searchForm,
        ...this.filterParam,
      };
      this.$api.common
        .doExport("exportReworkFrom", params)
        .then((res) => {
          if (res.code == 200) {
            this.$message.success("导出操作成功，请前往导出记录查看详情");
          }
        });
    },
    onSizeChange(val) {
      this.pageSize = val;
      this.pageNum = 1;
      this.getList();
    },
    onNumChange(val) {
      this.pageNum = val;
      this.getList();
    },
  },
};
</script>

<style lang="stylus" scoped></style>
