<template>
  <content-panel>
    <template v-slot:search>
      <search-box class="search-box">
        <el-form size="mini" :inline="true" :model="searchForm" ref="searchForm" label-position="right">
          <el-form-item label="员工姓名:" prop="staffName">
            <el-input clearable v-model.trim="searchForm.staffName" size="mini" @keyup.enter.native="onSearch">
              <template slot="append">
                <search-batch @seachFilter="onSearch('staffNames', $event)"
                  @focusEvent="focusEvent('staffNames', $event)" ref="childrenStaffNames" titleName="员工姓名" />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="厂牌编号:" prop="staffCode">
            <el-input v-model.trim="searchForm.staffCode" size="mini" clearable @keyup.enter.native="onSearch">
              <template slot="append">
                <search-batch @seachFilter="onSearch('staffCodes', $event)"
                  @focusEvent="focusEvent('staffCodes', $event)" ref="childrenStaffCodes" titleName="厂牌编号" />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="身份证号:" prop="idCard">
            <el-input clearable v-model.trim="searchForm.idCard" size="mini" @keyup.enter.native="onSearch">
              <template slot="append">
                <search-batch @seachFilter="onSearch('idCards', $event)" @focusEvent="focusEvent('idCards', $event)"
                  ref="childrenIdCard" titleName="身份证号" />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="所属工厂:" prop="factoryId">
            <el-select v-model="searchForm.factoryId" filterable clearable placeholder="请选择工厂" @change="onSearch">
              <el-option v-for="item in tabList" :key="item.id" :label="item.label" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="组织路径:" prop="orgPath">
            <el-input v-model="searchForm.orgPath" clearable placeholder="" @keyup.enter.native="onSearch"></el-input>
          </el-form-item>
          <el-form-item label="员工状态:" prop="status">
            <el-select v-model="searchForm.status" filterable clearable placeholder="请选择在职状态" @change="onSearch">
              <el-option v-for="item in activeOptions" :key="item.id" :label="item.label" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="冻结状态:" prop="freezeStatus">
            <el-select v-model="searchForm.freezeStatus" filterable clearable placeholder="请选择冻结状态" @change="onSearch">
              <el-option label="全部" value="0"> </el-option>
              <el-option label="未冻结" value="N"> </el-option>
              <el-option label="已冻结" value="Y"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="员工级别:" prop="iopWorkGrade">
            <el-input v-model="searchForm.iopWorkGrade" clearable @keyup.enter.native="onSearch"
              placeholder="请输入级别"></el-input>
          </el-form-item>
          <el-form-item label="员工类型:" prop="isSync">
            <el-radio-group v-model="searchForm.isSync" @change="onSearch">
              <el-radio label="0">全部 </el-radio>
              <el-radio label="1">正式员工 </el-radio>
              <el-radio label="2">试用员工 </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button  v-permission="'was-customized$manage$software$employee$searchView'" size="small" type="primary" @click="onSearch">
            查询
          </el-button>
          <el-button size="small" type="warning" @click="resetSearchForm">
            重置
          </el-button>
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <template v-slot:header-left>
        <ul>
          <li>
            <span>员工总人数:</span><span>{{ employeeStatisticInfo.platformTotal }}</span>
          </li>
          <li>
            <span>在职人数:</span><span>{{ employeeStatisticInfo.activeTotal }}</span>
          </li>
          <li>
            <span>正式员工:</span>{{ employeeStatisticInfo.syncTotal }}<span></span>
          </li>
          <li>
            <span>试工人数:</span><span>{{ employeeStatisticInfo.notSyncTotal }}</span>
          </li>
        </ul>
      </template>
      <template v-slot:header-right>
        <div ref="btnRight">
          <el-button size="small" v-permission="'was-customized$manage$software$employee$addView'" type="primary" @click="handleAdd"> 新增</el-button>
          <el-button size="small" v-permission="'was-customized$manage$software$employee$import'" type="primary" @click="handleImport"> 导入</el-button>
          <el-button size="small" v-permission="'was-customized$manage$software$employee$export'" type="primary" @click="handleExport"> 导出</el-button>
        </div>
      </template>
      <el-table stripe border v-loading="loading" ref="tableRef" highlight-current-row :height="maxTableHeight"
        :data="tableData">
        <el-table-column width="100" prop="staffName" label="员工姓名" align="left" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="staffCode" label="厂牌编号" align="left" width="120" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="idCard" label="身份证号" align="left" width="200">
          <template slot-scope="{ row }">
            <div class="idCardBox">
              <div class="idCard">
                {{ row.idCard }}
              </div>
            </div>
            <i v-if="row.idCard" :class="['iconfont', row.isShow ? 'icon-guanbi' : ' icon-yincangmima']"
              @click="toggleEye(row)"></i>
          </template>
        </el-table-column>
        <el-table-column prop="factoryName" label="所属工厂" align="left" width="120" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="orgPath" label="组织路径" align="left" width="200" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="abcNumber" label="农行卡号" align="left" width="200">
          <template slot-scope="{ row }">
            <div class="idCardBox">
              <div class="idCard">
                {{ filterData(row.abcNumber) }}
              </div>
            </div>
            <i v-if="row.abcNumber && row.abcNumber != -1" :class="[
              'iconfont',

              row.isShowabcNumber ? 'icon-guanbi' : ' icon-yincangmima',
            ]" @click="hideIdCards(row)"></i>
          </template>
        </el-table-column>
        <el-table-column prop="cebNumber" label="光大卡号" align="left" width="200">
          <template slot-scope="{ row }">
            <div class="idCardBox">
              <div class="idCard">
                {{ filterData(row.cebNumber) }}
              </div>
            </div>
            <i v-if="row.cebNumber && row.cebNumber != -1" :class="[
              'iconfont',
              row.isShowcebNumber ? 'icon-guanbi' : ' icon-yincangmima',
            ]" @click="
              hideIdCardss(row, !showListss[`isShow-${row.id}`], [`isShow-${row.id}`])
              "></i>
          </template>
        </el-table-column>
        <el-table-column prop="isManage" label="行管" align="left" width="50"></el-table-column>
        <el-table-column prop="iopWorkStation" label="职位" align="left" width="100" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="subProcess" label="子工序" align="left" width="80" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="productionCategory" label="员工类别" align="left" width="80" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="iopWorkGrade" label="员工级别" align="left" width="80" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="isSync" label="员工类型" align="left" width="80">
          <template slot-scope="scope">
            {{
              scope.row.isSync == 1 ? "正式员工" : scope.row.isSync == 2 ? "试工员工" : ""
            }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="员工状态" align="left" width="80">
          <template slot-scope="{ row }">
            {{ activeOptions[row.status - 1].label }}
          </template>
        </el-table-column>
        <el-table-column label="在职时间" align="left" width="180" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ filterDate(scope.row) }}
          </template>
        </el-table-column>
        <el-table-column label="冻结状态" prop="freezeStatus" width="80"></el-table-column>
        <el-table-column align="left" fixed="right" width="220">
          <template slot-scope="scope">
            <div style="display: block">
              <el-button v-permission="'was-customized$manage$software$employee$updateView'" size="small" type="text" @click="handleEdit(scope.row)">
                编辑
              </el-button>
              <el-button v-permission="'was-customized$manage$software$employee$history'" size="small" type="text" @click="historyData(scope.row)">
                历史数据
              </el-button>
              <el-button size="small" type="text"
                v-permission="'was-customized$manage$software$employee$delete'"
                v-show="scope.row.staffCode && scope.row.staffCode.includes('SG') && scope.row.isSync == 2"
                @click="handleDelete(scope.$index, scope.row)">
                删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <div style="text-align: right" ref="pagination">
          <el-pagination @size-change="onSizeChange" @current-change="onNumChange" :current-page="pageNum"
            :page-size="pageSize" :total="total" :page-sizes="[50, 100, 200]"
            layout="total, sizes, prev, pager, next, jumper">
          </el-pagination>
        </div>
      </template>
    </table-panel>
    <add-dialog v-if="visible" :isVisible="visible" :title="title" :editForm="editForm"
      @cancel="handleCancel"></add-dialog>
    <history-dialog v-if="isVisible" :isVisible="isVisible" :editForm="editForm"
      @historyCancel="historyCancel"></history-dialog>
    <Import v-if="ImportVisible" :visible="ImportVisible" @cancel="cancel" @confirm="confirm"
      :importInfo="importInfo" />
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import moment from "moment";
import addDialog from "./addDialog";
import historyDialog from "./historyDialog";
export default {
  name: "softwareEmployee",
  mixins: [tableMixin, pagePathMixin],
  components: {
    addDialog,
    historyDialog,
  },
  data() {
    return {
      searchForm: {
        status: "",
        isSync: "0",
        staffCode: "",
        staffName: "",
        idCard: "",
        factoryId: "",
        freezeStatus: "0",
        iopWorkGrade: "",
        orgPath: ""
      },
      editForm: {},
      visible: false,
      ImportVisible: false,
      isVisible: false,
      title: "",
      tableData: [],
      //在职状态
      activeOptions: [
        {
          label: "在职",
          id: "1",
        },
        {
          label: "离职",
          id: "2",
        },
        {
          label: "停薪留职",
          id: "3",
        },
        {
          label: "退休",
          id: "4",
        },
      ],
      tabList: [],
      loading: false,
      filterParam: {},
      params: {},
      employeeStatisticInfo: {}, //员工统计
      pageSize: 50,
      pageNum: 1,
      total: 0,
      resizeOffset: 55,
      showListss: {},
      importInfo: {
        reportName: "softwareStaffimport",
        paramMap: {
          columnValue: "员工管理",
        },
      },
    };
  },
  created() {
    this.$api.softwareSystemManage.getBasicPermission.getBasicPermissionAll().then((res) => {
      if (res.code === 200 && res.data) {
        this.tabList = res.data.map((item) => ({
          label: item.name,
          name: item.name,
          id: item.id,
          process: item.process,
        }));
      }
    });
    this.getList();
    this.getStatisticsList();
  },
  methods: {
    filterDate(row) {
      let entryTime =
        (row.entryTime && moment(row.entryTime).format("YYYY-MM-DD")) || "-";
      let dimissionTime =
        row.dimissionTime && moment(row.dimissionTime).format("YYYY-MM-DD");
      return (
        entryTime +
        (dimissionTime ? `至${dimissionTime}` : row.status == 1 ? "至今" : "至-")
      );
    },
    //获取员工列表
    getList() {
      this.loading = true;
      this.$api.softwareInformation.employee
        .employeeList({
          pageSize: this.pageSize,
          pageNum: this.pageNum,
          filterData: {
            ...this.filterParam,
            ...this.params,
            isSync: this.searchForm.isSync == "0" ? "" : this.searchForm.isSync,
            freezeStatus:
              this.searchForm.freezeStatus == "0" ? "" : this.searchForm.freezeStatus,
          },
        })
        .then((res) => {
          if (res.code === 200) {
            this.tableData =
              (res.data &&
                res.data.list.map((item) => ({
                  ...item,
                  idCardOrigin: item.idCard,
                  idCardDecoded: "",
                  abcNumberOrigin: item.abcNumber,
                  abcNumberDecoded: "",
                  cebNumberOrigin: item.cebNumber,
                  cebNumberDecoded: "",
                  isShow: false,
                  isShowabcNumber: false,
                  isShowcebNumber: false,
                }))) ||
              [];
            this.total = res.data.total;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //获取员工统计
    getStatisticsList() {
      this.$api.softwareInformation.employee
        .employeeStatistics({
          ...this.filterParam,
          ...this.params,
          isSync: this.searchForm.isSync == "0" ? "" : this.filterParam.isSync,
          freezeStatus:
            this.searchForm.freezeStatus == "0" ? "" : this.searchForm.freezeStatus,
        })
        .then((res) => {
          if (res.code === 200) {
            this.employeeStatisticInfo = res.data
              ? res.data
              : {
                activeTotal: "",
                notSyncTotal: "",
                platformTotal: "",
                syncTotal: "",
              };
          }
        });
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.$refs.childrenStaffNames.resetFilter();
      this.$refs.childrenStaffCodes.resetFilter();
      this.$refs.childrenIdCard.resetFilter();
      this.filterParam = {};
      this.params = {};
      this.onSearch();
    },
    //搜索
    onSearch(name, data) {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.filterParam[name] = data;
      }
      this.pageNum = 1;
      this.getList();
      this.getStatisticsList();
    },
    focusEvent(name, data) {
      if (Array.isArray(data) && !data.length) {
        this.params[name] = [];
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.params[name] = data;
      }
    },
    filterData(value) {
      if (!value || value == "-1") return "";
      return value;
    },
    //身份证解码
    toggleEye(row) {
      if (row.isShow) {
        row.idCard = row.idCardOrigin;
        row.isShow = false;
      } else {
        if (row.idCardDecoded) {
          row.idCard = row.idCardDecoded;
          row.isShow = true;
          return;
        }
        if (row.staffCode.includes("SG")) {
          this.$api.information.employee
            .idCardDecodeStaff({ idCardRsa: row.idCardRsa })
            .then((res) => {
              row.idCard = res.data;
              row.idCardDecoded = res.data;
              row.isShow = true;
            });
        } else {
          this.$api.information.employee.decrypt(row.idCardRsa).then((res) => {
            row.idCard = res.data;
            row.idCardDecoded = res.data;
            row.isShow = true;
          });
        }
      }
    },
    //农行卡号解码
    hideIdCards(row) {
      if (row.isShowabcNumber) {
        row.abcNumber = row.abcNumberOrigin;
        row.isShowabcNumber = false;
      } else {
        if (row.abcNumberDecoded) {
          row.abcNumber = row.abcNumberDecoded;
          row.isShowabcNumber = true;
          return;
        }
        var formData = new FormData();
        formData.append("cardRsa", row.abcNumberRsa);
        this.$api.information.employee.idCardDecrypt(formData).then((res) => {
          row.abcNumber = res.data;
          row.abcNumberDecoded = res.data;
          row.isShowabcNumber = true;
        });
      }
    },
    //光大卡号解码
    hideIdCardss(row) {
      if (row.isShowcebNumber) {
        row.cebNumber = row.cebNumberOrigin;
        row.isShowcebNumber = false;
      } else {
        if (row.cebNumberDecoded) {
          row.cebNumber = row.cebNumberDecoded;
          row.isShowcebNumber = true;
          return;
        }
        var formData = new FormData();
        formData.append("cardRsa", row.cebNumberRsa);
        this.$api.information.employee.idCardDecrypt(formData).then((res) => {
          row.cebNumber = res.data;
          row.cebNumberDecoded = res.data;
          row.isShowcebNumber = true;
        });
      }
    },
    //新增
    handleAdd() {
      this.visible = true;
      this.title = "新增员工";
    },
    //编辑
    handleEdit(row) {
      this.visible = true;
      this.title = "编辑员工";
      this.editForm = {
        id: row.id,
        isSync: row.isSync,
      };
    },
    //历史数据
    historyData(row) {
      this.isVisible = true;
      this.editForm = {
        staffCode: row.staffCode,
      };
    },
    //删除
    handleDelete(index, row) {
      this.$confirm("此操作将永久删除该条数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$api.information.employee
          .deleteEmployee({ id: row.id, staffCode: row.staffCode })
          .then((res) => {
            if (res.code === 200) {
              this.getList();
              this.getStatisticsList();
              this.$message({
                type: "success",
                message: "删除成功!",
              });
            }
          });
      });
    },
    handleCancel(type) {
      this.visible = false;
      if (type == "cancel") return;
      this.getList();
      this.getStatisticsList();
    },
    historyCancel() {
      this.isVisible = false;
    },

    //导入
    handleImport() {
      this.title = "导入";
      this.ImportVisible = true;
    },
    //导出
    handleExport() {
      let params = {};
      if (Object.keys(this.filterParam).length) {
        params = {
          ...this.filterParam,
          isSync: this.filterParam.isSync == "0" ? "" : this.filterParam.isSync,
          freezeStatus:
            this.searchForm.freezeStatus == "0" ? "" : this.searchForm.freezeStatus,
        };
      }
      this.$api.common
        .doExport("softwareExportstaff", { ...params, ...this.params })
        .then((res) => {
          if (res.code == 200) {
            this.$message.success("导出成功，请稍后到导出列表查阅");
          }
        });
    },
    onSizeChange(val) {
      this.pageSize = val;
      this.pageNum = 1;
      this.getList();
    },
    onNumChange(val) {
      this.pageNum = val;
      this.getList();
    },
    cancel(value) {
      this.ImportVisible = value;
    },
    confirm(value) {
      this.ImportVisible = value;
    },
  },
};
</script>

<style lang="stylus" scoped>
>>> .el-form--inline .el-form-item {
  margin-right: 40px;
}

>>>.el-tabs__nav-wrap::after {
  height: 0;
}

ul {
  list-style: none;
  display: flex;
  align-items: center;
  padding: 0;
  margin: 0
  li{
    margin-right: 10px;
  }
}

>>>.el-table__row {
  td {
    &:nth-child(3),&:nth-child(6),&:nth-child(7){
      .cell {
        display: flex;
        justify-content: space-between;

        .idCardBox {
          width: 80%;

          .idCard {
            display: block;
            cursor: pointer;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
          }
        }
      }
    }
  }
}
</style>
