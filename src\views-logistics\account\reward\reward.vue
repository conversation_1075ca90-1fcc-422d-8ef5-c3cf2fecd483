<template>
  <!-- 奖惩台账 -->
  <content-panel>
    <template v-slot:search>
      <search-box class="search-box">
        <el-form
          size="mini"
          :inline="true"
          :model="searchForm"
          label-width="103px"
          ref="searchForm"
          label-position="right"
        >
          <el-form-item label="员工姓名:" prop="staffName">
            <el-input
              clearable
              v-model.trim="searchForm.staffName"
              size="mini"
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffNames', $event)"
                  @focusEvent="focusEvent('staffNames', $event)"
                  ref="childrenStaffNames"
                  titleName="员工姓名"
                />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="厂牌编号:" prop="staffCode">
            <el-input
              v-model.trim="searchForm.staffCode"
              size="mini"
              clearable
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffCodes', $event)"
                  @focusEvent="focusEvent('staffCodes', $event)"
                  ref="childrenStaffCodes"
                  titleName="厂牌编号"
                />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="责任分厂:" prop="factoryId">
            <el-select
              v-model="searchForm.factoryId"
              filterable
              clearable
              placeholder="请选择责任分厂"
              @change="onSearch"
            >
              <el-option
                v-for="item in tabList"
                :key="item.value"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="执行年月:" prop="accountingMonth">
            <el-date-picker
              v-model="searchForm.accountingMonth"
              type="month"
              placeholder="选择执行年月"
              @change="onSearch"
            >
            </el-date-picker>
          </el-form-item>
          <!-- <el-form-item label="实际执行分厂:" prop="actualFactoryId">
            <el-select
              v-model="searchForm.actualFactoryId"
              filterable
              clearable
              placeholder="请选择实际执行分厂"
              @change="onSearch"
            >
              <el-option
                v-for="item in tabList"
                :key="item.value"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item> -->
          <!-- <el-form-item label="实际执行年月:" prop="actualMonth">
            <el-date-picker
              v-model="searchForm.actualMonth"
              type="month"
              placeholder="实际执行年月"
              @change="onSearch"
            >
            </el-date-picker>
          </el-form-item> -->
          <el-form-item label="金额范围:" prop="amountRangeList">
            <el-select
              v-model="searchForm.amountRangeList"
              filterable
              clearable
              placeholder="请选择金额范围"
              @change="onSearch"
              multiple
            >
              <el-option
                v-for="item in payList"
                :key="item.value"
                :label="item.label"
                :value="item"
              >
              </el-option>
            </el-select>
          </el-form-item>
              <!-- 组织路径 -->
              <el-form-item label="组织路径:" prop="orgPath">
            <el-input
              v-model="searchForm.orgPath"
              size="mini"
              clearable
              maxlength="100"
              placeholder="请输入组织路径关键词查询"
            >
            </el-input>
          </el-form-item>
          <el-form-item label="奖惩类型:" prop="type">
            <el-radio-group v-model="searchForm.type" @change="onSearch">
              <el-radio label="0">全部</el-radio>
              <el-radio label="1">奖励</el-radio>
              <el-radio label="2">处罚</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button
            size="small"
            type="primary"

            @click="onSearch"
          >
            查询
          </el-button>
          <el-button size="small" type="warning" @click="resetSearchForm">
            重置
          </el-button>
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <template v-slot:header-left>
        <el-tabs v-model="status">
          <el-tab-pane
            v-for="item in items"
            :key="item.name"
            :name="item.value"
          >
            <span slot="label">{{
              item.number != null ? `${item.name}(${item.number})` : item.name
            }}</span>
          </el-tab-pane>
        </el-tabs>
      </template>
      <template v-slot:header-right>
        <div ref="btnRight">
          <el-checkbox
            v-model="selectall"
            type="primary"
            label="全选"
            border
            size="mini"
            style="margin-right: 10px"
            v-if="['0', '1'].includes(status)"
            class="select"
          ></el-checkbox>
          <el-button
            v-show="status == '1'"
            v-permission="
              'was-customized$informationAccount$logistics$reward$add'
            "
            size="small"
            type="primary"
            @click="handleAdd"
          >
            新增
          </el-button>
          <el-button
            v-show="status == '1'"
            v-permission="
              'was-customized$informationAccount$logistics$reward$add'
            "
            size="small"
            type="primary"
            @click="handleImport"
          >
            导入
          </el-button>
          <el-button
            v-show="status != '4'"
            v-permission="
              'was-customized$informationAccount$logistics$reward$add'
            "
            size="small"
            type="primary"
            @click="handleExport"
          >
            导出
          </el-button>
          <el-button
            v-show="status == '0'"
            v-permission="
              'was-customized$informationAccount$customized$reward$add'
            "
            size="small"
            type="primary"
            @click="batchDelete"
          >
            批量删除
          </el-button>
          <el-button
            v-permission="
              'was-customized$informationAccount$logistics$reward$add'
            "
            v-show="status == '0'"
            size="small"
            type="primary"
            @click="batchClculate"
          >
            批量核算
          </el-button>
          <el-button
            v-permission="
              'was-customized$informationAccount$logistics$reward$add'
            "
            size="small"
            type="primary"
            @click="batchBack"
            v-show="status == '1'"
          >
            批量回退
          </el-button>
          <el-button
            size="small"
            type="primary"
            @click="batchconfirmation"
            v-show="status == '4'"
          >
            批量确认
          </el-button>
        </div>
      </template>
      <el-table
        stripe
        border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :height="maxTableHeight"
        :data="tableData"
        @select-all="handleSelectionAll"
        @selection-change="handleSelectionChange"
        :row-key="getRowKeys"
        :key="tableKey"
      >
        <el-table-column width="40" type="selection"> </el-table-column>
        <el-table-column
          prop="code"
          label="奖惩编号"
          width="130"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column prop="staffName" label="姓名" width="100" align="left">
        </el-table-column>
        <el-table-column
          prop="staffCode"
          label="厂牌编号"
          width="110"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          v-if="['1', '2'].includes(status)"
          prop="actualStaffCode"
          label="实际奖惩厂牌"
          width="110"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="factoryName"
          label="责任分厂"
          width="100"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="orgPath"
          label="组织路径"
          width="130"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="iopWorkStation"
          label="职位"
          width="100"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="iopWorkGrade"
          label="级别"
          width="100"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="workStatus"
          label="是否在职"
          width="100"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          label="离职日期"
          width="100"
          align="left"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            {{ row.dimissionTime | shortDate }}
          </template>
        </el-table-column>
        <el-table-column
          prop="staffStatusType"
          label="离职类型"
          width="100"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="accountingMonth"
          label="执行年月"
          width="80"
          align="left"
        >
        </el-table-column>
        <el-table-column
          label="奖惩日期"
          width="100"
          align="left"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            {{ row.rewardDate | shortDate }}
          </template>
        </el-table-column>
        <el-table-column
          label="奖惩金额"
          width="110"
          align="left"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            {{ row.amount | moneyFormat }}
          </template>
        </el-table-column>

        <el-table-column
          v-if="['1', '2'].includes(status)"
          label="实际奖惩金额"
          width="120"
          align="left"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            {{ row.actualAmount | moneyFormat }}
          </template>
        </el-table-column>

        <el-table-column
          v-if="['1', '2'].includes(status)"
          prop="payedAmount"
          label="已奖惩金额  "
          width="120"
          align="left"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            {{ row.payedAmount | moneyFormat }}
          </template>
        </el-table-column>

        <el-table-column
          v-if="['1', '2'].includes(status)"
          prop="payingAmount"
          label="待奖惩金额"
          width="105"
          align="left"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            {{ row.payingAmount | moneyFormat }}
          </template>
        </el-table-column>

        <el-table-column prop="type" label="奖惩类型" width="100" align="left">
        </el-table-column>

        <el-table-column
          prop="fileNo"
          label="文件编号"
          width="100"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="reason"
          label="奖惩原因"
          width="100"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>

        <el-table-column
          prop="comments"
          label="备注说明"
          width="100"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          v-if="status == 4"
          prop="dataType"
          label="变动说明"
          width="80"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          v-if="status == 4"
          label="处理状态"
          width="80"
          align="left"
        >
          <template slot-scope="{ row }">
            {{ row.handleStatus == "0" ? "未处理" : "已处理" }}
          </template>
        </el-table-column>
        <el-table-column
          prop="handleName"
          label="办理人员"
          width="80"
          align="left"
        >
        </el-table-column>
        <el-table-column
          prop="handleTime"
          label="办理时间"
          width="150"
          align="left"
        >
          <template slot-scope="{ row }">
            {{ row.handleTime | dateFormat }}
          </template>
        </el-table-column>
        <el-table-column
          prop="acceptTime"
          label="接收时间"
          width="150"
          align="left"
        >
          <template slot-scope="scope">
            {{ scope.row.acceptTime | dateFormat }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="160" align="left" fixed="right">
          <template slot-scope="{ row }">
            <!-- 核算按钮权限 -->

            <el-button
              v-show="status != '4' && row.status == '未处理'"
              v-permission="
                'was-customized$informationAccount$logistics$reward$add'
              "
              style="margin-left: 0"
              size="small"
              type="text"
              @click="handleClculate(row)"
            >
              核算
            </el-button>
            <!-- <el-button
              v-permission="
                'was-customized$informationAccount$logistics$reward$add'
              "
              style="margin-left: 0"
              size="small"
              type="text"
              v-show="status != '4' && row.status != '未处理'"
              @click="handleBack(row)"
            >
              退回
            </el-button> -->
            <el-button
              v-permission="
                'was-customized$informationAccount$logistics$reward$add'
              "
              style="margin-left: 0"
              size="small"
              type="text"
              v-show="
                status != '4' &&
                row.status == '未处理' &&
                row.code.includes('X')
              "
              @click="handleDelete(row)"
            >
              删除
            </el-button>
            <el-button
              v-permission="
                'was-customized$informationAccount$logistics$reward$add'
              "
              style="margin-left: 0"
              size="small"
              type="text"
              v-show="status != '4' && row.status != '未处理'"
              @click="handleDetails(row)"
            >
              查看详情
            </el-button>
            <template v-if="status != '4' && row.status != '未处理'">
              <el-button
              v-permission="
                'was-customized$informationAccount$logistics$reward$add'
              "
              style="margin-left: 0"
              size="small"
              type="text"
              v-show="status != '4' && row.status == '已处理'"
              @click="handleBack(row)"
            >
              退回
            </el-button>
            <el-button
              v-permission="
                'was-customized$informationAccount$logistics$reward$add'
              "
              style="margin-left: 0"
              size="small"
              type="text"
              v-show="status != '4' && row.status == '已完成' && row.isAccounting == 0"
              @click="handleBack(row)"
            >
              退回
            </el-button>
            </template>
            <el-button
              v-permission="
                'was-customized$informationAccount$logistics$reward$add'
              "
              style="margin-left: 0"
              size="small"
              type="text"
              v-show="status == '4' && row.handleStatus == '0'"
              @click="handleConfirm(row)"
            >
              确认
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <div style="text-align: right">
          <el-pagination
            @size-change="onSizeChange"
            @current-change="onNumChange"
            :current-page="pageNum"
            :page-size="pageSize"
            :total="total"
            :page-sizes="[50, 100, 200, 500]"
            layout="total, sizes, prev, pager, next, jumper"
          >
          </el-pagination>
        </div>
      </template>
    </table-panel>
    <!-- 新增 编辑-->
    <add-dialog
      v-if="visible"
      :isVisible="visible"
      :title="title"
      :editForm="editForm"
      @cancel="handleCancel"
    >
    </add-dialog>
    <!-- 核算 详情-->
    <calculate
      v-if="calculateVisible"
      :isVisible="calculateVisible"
      :title="title"
      :editForm="editForm"
      @cancel="calculateCancel"
    >
    </calculate>
    <common-dialog
      v-if="commonVisible"
      :isVisible="commonVisible"
      :title="title"
      :info="info"
      :selectall="selectall"
      :filterParam="filterParam"
      @cancel="commonCancel"
      :status = status
      :commonData="commonData"
    ></common-dialog>
    <Import
      v-if="importVisible"
      :visible="importVisible"
      @cancel="cancel"
      @confirm="confirm"
      :importInfo="importInfo"
    />
    <!-- 查看详情 -->
    <details-dialog
      v-if="isVisibleDteails"
      :visible="isVisibleDteails"
      :editForm="editForm"
      @cancel="detailsCancel"
    ></details-dialog>
    <!-- 批量核算 -->
    <accoun-dialog
      v-if="accounDialogVisible"
      :isVisible="accounDialogVisible"
      :commonData="commonData"
      @cancel="accounCancel"
      :selectall="selectall"
      :filterParam="filterParam"
      @success="successPage"
      :total="total"
    ></accoun-dialog>
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import moment from "moment";
import { moneyFormat, moneyDelete } from "@/utils";
import addDialog from "./addDialog";
import calculate from "./calculate";
import commonDialog from "./commonDialog";
import detailsDialog from "./detailsDialog";
import accounDialog from "./accounDialog";

export default {
  name: "LogisticsReward",
  mixins: [tableMixin,pagePathMixin],
  components: {
    calculate,
    addDialog,
    commonDialog,
    detailsDialog,
    accounDialog,
  },
  data() {
    return {
      selectall: false,
      searchForm: {
        staffCode: "",
        staffName: "",
        factoryId: "",
        accountingMonth: moment().subtract(1, "month").format("YYYY-MM"),
        // actualFactoryId: "",
        type: "0",
        amountRangeList: "",
          //组织路径
          orgPath:"",
      },
      editForm: {},
      detailsData: {},
      accounDialogVisible: false,
      payList: [
        {
          label: "0~100元",
          value: "0",
          max: 100,
          min: 0,
        },
        {
          label: "100~300元",
          value: "1",
          max: 300,
          min: 100,
        },
        {
          label: "300~500元",
          value: "2",
          max: 500,
          min: 300,
        },
        {
          label: "500元以上",
          value: "3",
          max: "",
          min: 500,
        },
      ],
      items: [
        {
          name: "未处理",
          value: "0",
          type: "unCheck",
          number: 0,
        },
        {
          name: "已处理",
          value: "1",
          type: "checked",
          number: 0,
        },
        {
          name: "已完成",
          value: "2",
          type: "inReview",
          number: 0,
        },
        // {
        //   name: "已归档",
        //   value: "3",
        //   type: "audited",
        // },
        {
          name: "变动数据",
          value: "4",
          type: "audited",
        },
      ],
      status: "0",
      tabList: [],
      filterParam: {},
      params: {},
      importInfo: {
        reportName: "logisticsImportrewardpunishment",
        paramMap: {
          columnValue: "奖惩台账",
        },
      },
      //表格数据
      tableData: [],
      idList: [],
      info: {},
      loading: false,
      // resizeOffset: 53,
      pageSize: 50,
      pageNum: 1,
      total: 0,
      title: "",
      visible: false,
      backVisible: false,
      calculateVisible: false,
      commonVisible: false,
      importVisible: false,
      isAll: false,
      diffTime: 0,
      startTime: 0,
      tableKey: "",
      isVisibleDteails: false,
    };
  },
  created() {
    this.$nextTick(() => {
      this.$api.logisticsInformation.rewardLedger
        .listLogisticsFactory()
        .then((res) => {
          if (res.code === 200) {
            this.tabList = res.data.map((item) => ({
              label: item.name,
              name: item.name,
              id: item.id,
            }));
          }
        });
    });
    this.onSearch();
  },
  watch: {
    $route: {
      handler(value) {
        if (value.path.includes("reward") && value.path.includes("logistics")) {
          this.getList();
          this.getStatistics();
        }
      },
      deep: true,
    },
    status: {
      handler(value) {
        this.selectall = false;
        this.$refs.tableRef.clearSelection();
        this.idList = [];
        this.onSearch();
        this.getStatistics();
      },
    },
    selectall: {
      handler() {
        if (this.selectall) {
          this.$refs.tableRef.toggleAllSelection();
        } else {
          this.$refs.tableRef.clearSelection();
        }
      },
    },
  },
  methods: {
    getRowKeys(row) {
      return row.id;
    },
    //奖惩台账列表
    getList() {
      if (this.status == 4 && !this.searchForm.accountingMonth) {
        this.searchForm.accountingMonth = moment()
          .subtract(1, "month")
          .format("YYYY-MM");
        this.filterParam.accountingMonth = moment()
          .subtract(1, "month")
          .format("YYYY-MM");
      }
      this.loading = true;
      this.tableData = [];
      this.tableKey = Math.random();
      if (this.status == 4) {
        this.$api.logisticsInformation.rewardLedger
          .hrChangeData({
            pageNum: this.pageNum,
            pageSize: this.pageSize,
            filterData: {
              ...this.filterParam,
              ...this.params,
              type:
                this.searchForm.type == "0" ? "" : +this.searchForm.type - 1,
            },
          })
          .then(({ data: { list, total } }) => {
            this.tableData = list || [];
            this.total = total;
            if (this.selectall) {
              this.$refs.tableRef.toggleAllSelection();
            }
          })
          .finally(() => {
            this.loading = false;
          });
        return;
      }
      this.$api.logisticsInformation.rewardLedger
        .rewardList({
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          filterData: {
            ...this.filterParam,
            ...this.params,
            status: this.status,
            type: this.searchForm.type == "0" ? "" : +this.searchForm.type - 1,
          },
        })
        .then(({ data: { list, total } }) => {
          this.tableData = list || [];
          this.total = total;
          if (this.selectall) {
            this.tableData.forEach((row) => {
              this.$refs.tableRef.toggleRowSelection(row, true);
            });
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //获取统计信息
    async getStatistics() {
      let params = {
        ...this.filterParam,
        ...this.params,
        type: this.searchForm.type == "0" ? "" : +this.searchForm.type - 1,
      };
      const list =
        await this.$api.logisticsInformation.rewardLedger.rewardStatistic(
          params
        );
      const data = list.data || {};
      if (Object.keys(data).length != 0) {
        this.items = this.items.map((item) => {
          for (const key in data) {
            if (item.type == key) {
              item.number = this.getTotal(list.data[item.type]);
            }
          }
          return item;
        });
      } else {
        this.items = [
          {
            name: "未处理",
            value: "0",
            type: "unCheck",
            number: 0,
          },
          {
            name: "已处理",
            value: "1",
            type: "checked",
            number: 0,
          },
          {
            name: "已完成",
            value: "2",
            type: "inReview",
            number: 0,
          },
          // {
          //   name: "已归档",
          //   value: "3",
          //   type: "audited",
          // },
          {
            name: "变动数据",
            value: "4",
            type: "audited",
          },
        ];
      }
    },
    getTotal(num) {
      if (isNaN(Number(num))) return 0;
      return Number(num) > 9999 ? "9999+" : num;
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.$refs.childrenStaffNames.resetFilter();
      this.$refs.childrenStaffCodes.resetFilter();
      this.filterParam = {};
      this.params = {};
      this.searchForm.accountingMonth = moment()
        .subtract(1, "month")
        .format("YYYY-MM");
      this.onSearch();
    },
    //搜索
    onSearch(name, data) {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (key === "accountingMonth") {
          if (val) this.filterParam[key] = moment(val).format("YYYY-MM");
        } else if (key === "amountRangeList" && val.length) {
          this.filterParam.amountRangeList = val.map((item) => {
            return {
              maxAmount: item.max,
              minAmount: item.min,
            };
          });
        } else if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0)
          this.filterParam[name] = data;
      }
      this.pageNum = 1;
      this.getList();
      this.getStatistics();
    },
    focusEvent(name, data) {
      if (Array.isArray(data) && !data.length) {
        this.params[name] = [];
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) {
          this.params[name] = data;
        }
      }
    },
    //查看详情
    handleDetails({ id }) {
      this.isVisibleDteails = true;
      this.editForm = {
        id,
      };
    },
    //新增
    handleAdd() {
      this.visible = true;
    },
    //批量删除
    batchDelete() {
      if (this.idList.length === 0 && !this.selectall) {
        this.$message({
          message: "请先勾选需要批量确认的内容",
          type: "warning",
        });
        return;
      }
      this.info = { idList: this.idList, type: "删除" };
      this.title = "批量删除";
      this.commonVisible = true;
    },
    //核算
    handleClculate(row) {
      this.editForm = {
        id: row.id,
      };
      this.title = "核算";
      this.calculateVisible = true;
    },
    //批量核算
    batchClculate() {
      if (this.idList.length === 0 && !this.selectall) {
        this.$message({
          message: "请先勾选需要批量确认的内容",
          type: "warning",
        });
        return;
      }
      this.commonData = { idList: this.idList, type: "核算" };
      this.title = "批量核算";
      this.accounDialogVisible = true;
    },
    //退回
    handleBack(row) {
      // this.$api.logisticsInformation.rewardLedger
      //   .rollback({ ids: [row.id] || "" })
      //   .then(() => {
      //     this.$notify.success({
      //       title: "成功",
      //       message: "退回成功",
      //     });
      //     this.getList();
      //     this.getStatistics();
      //   });
      this.title = "退回";
      this.commonVisible = true;
      this.commonData = { id:row.id };
    },
    //批量退回
    batchBack() {
      if (this.idList.length === 0 && !this.selectall) {
        this.$message({
          message: "请先勾选需要批量退回的内容",
          type: "warning",
        });
        return;
      }
      this.info = { idList: this.idList, type: "退回" };
      this.title = "批量退回";
      this.commonVisible = true;
    },
    //删除
    handleDelete(row) {
      this.$confirm("是否确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$api.logisticsInformation.rewardLedger
          .deleteReward({
            id: row.id,
          })
          .then(() => {
            this.$notify.success({
              title: "成功",
              message: "删除成功",
            });
            this.getList();
            this.getStatistics();
          });
      });
    },
    detailsCancel() {
      this.isVisibleDteails = false;
    },
    //批量确认
    batchconfirmation(){
      if (this.idList.length === 0 ) {
        this.$message({
          message: "请先勾选需要批量确认的内容",
          type: "warning",
        });
        return;
      }
      this.info = { idList: this.idList, type: "确认" };
      this.title = "批量确认";
      this.commonVisible = true;
    },
    //确认
    handleConfirm(row) {
      this.$confirm("是否确认?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$api.logisticsInformation.rewardLedger
          .rewardPunish({ id: row.id })
          .then(() => {
            this.$notify.success({
              title: "成功",
              message: "确认成功",
            });
            this.getList();
            this.getStatistics();
          });
      });
    },
    accounCancel() {
      this.accounDialogVisible = false;
    },
    successPage() {
      this.accounDialogVisible = false;
      this.$refs.tableRef.clearSelection();
      this.onSearch();
    },
    //导入
    handleImport() {
      this.importVisible = true;
    },
    //导出
    handleExport() {
      let params = {
        status: this.status,
      };
      if (Object.keys(this.filterParam).length) {
        params = {
          ...params,
          ...this.filterParam,
          type: this.searchForm.type == "0" ? "" : +this.searchForm.type - 1,
        };
      }
      this.$api.common
        .doExport("logisticsExportrewardpunishmentledger", {
          ...params,
          ...this.params,
        })
        .then((res) => {
          this.$message.success("导出操作成功，请前往导出记录查看详情");
        });
    },
    cancellation(type) {
      if (type && type == "cancel") return;
      this.getList();
      this.getStatistics();
    },
    handleCancel(obj) {
      const { type, isVisible } = obj;
      this.visible = isVisible;
      this.cancellation(type);
    },
    calculateCancel(obj) {
      const { type, isVisible } = obj;
      this.calculateVisible = isVisible;
      this.cancellation(type);
    },
    commonCancel(type) {
      this.$refs.tableRef.clearSelection();
      this.commonVisible = false;
      if (type == "cancel") return;
      this.getList();
      this.getStatistics();
    },
    handleSelectionAll(val) {
      this.idList = val && val.map((item) => item.id);
    },
    handleSelectionChange(val) {
      this.idList = val && val.map((item) => item.id);
    },
    cancel(value) {
      this.importVisible = value;
    },
    confirm(value) {
      this.importVisible = value;
      this.getList();
    },
    onSizeChange(val) {
      this.pageSize = val;
      this.pageNum = 1;
      this.getList();
    },
    onNumChange(val) {
      this.pageNum = val;
      this.getList();
    },
  },
};
</script>

<style lang="stylus" scoped>
>>> .el-form--inline .el-form-item {
  margin-right: 40px;
}
.select{
  background:#0bb78e
  color:#fff
}
>>>.el-checkbox.is-bordered.el-checkbox--mini{
    height:25px !important
}
>>>.el-checkbox__input.is-checked+.el-checkbox__label{
    color:white !important
}
>>>.el-checkbox__input.is-checked .el-checkbox__inner{
   border-color:white !important
}
</style>
