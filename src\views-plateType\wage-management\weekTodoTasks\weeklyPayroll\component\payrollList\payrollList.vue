<template>
  <!--周工资表 -->
  <content-panel>
    <table-panel ref="tablePanel">
      <div class="header_tableName">{{ filterName }}</div>
      <el-table :key="tableKey" stripe border v-loading="loading" ref="tableRef" highlight-current-row :data="tableData"
        :height="maxTableHeight" style="width: 100%">
        <el-table-column prop="bigProcessCode" label="大工序编码" align="left" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="bigProcessName" label="大工序名称" align="left" show-overflow-tooltip>
        </el-table-column>
        <el-table-column label="本厂出勤天数" prop="totalWork" align="left">
        </el-table-column>
        <el-table-column label="本厂加班小时" prop="totalOvertime" align="left">
        </el-table-column>
        <el-table-column label="工作日出勤(天)" prop="workdayWork" align="left">
        </el-table-column>
        <el-table-column label="工作日延时加班(小时)" width="160" prop="workdayOvertime" align="left" show-overflow-tooltip>
        </el-table-column>
        <el-table-column label="周末出勤(天)" prop="weekWork">
        </el-table-column>
        <el-table-column label="周末延时加班(小时)" prop="weekOvertime">
        </el-table-column>
        <el-table-column label="节假日出勤(天)" prop="holidayWork">
        </el-table-column>
        <el-table-column label="节假日延时加班(小时)" width="160" prop="holidayOvertime">
        </el-table-column>
        <el-table-column label="计件工资" prop="pieceWage">
          <template slot-scope="{ row }">
            {{ filterData(row.pieceWage || 0) }}
          </template>
        </el-table-column>
        <el-table-column label="日均计件" prop="dailyPieceWage">
          <template slot-scope="{ row }">
            {{ filterData(row.dailyPieceWage || 0) }}
          </template>
          <template slot="header">
            <span>日均计件</span>
            <el-tooltip placement="top">
              <div slot="content">
                日均计件=计件工资/本厂考勤天数
              </div>
              <i class="el-icon-question"></i>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      <!-- <el-pagination :current-page="pageNum" :page-size="pageSize" :total="total" :page-sizes="[50, 100, 200]"
        @size-change="handleSizeChange" @current-change="handleCurrentChange"
        layout="total, sizes, prev, pager, next, jumper">
      </el-pagination> -->
    </table-panel>
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import { moneyFormat } from "@/utils";
export default {
  name: "PlateTypePayrollList",
  mixins: [tableMixin, pagePathMixin],
  data() {
    return {
      tableData: [],
      tableKey: 0,
      loading: false,
      pageSize: 1000, //每页显示个数
      pageNum: 1, //当前页数
      filterName: ''
    };
  },

  watch: {
    $route: {
      handler(value) {
        if (
          value.path.includes("weeklyPayroll") &&
          value.path.includes("plateType")
        ) {
          let data = this.parseRouteQueryData();
          this.filterName = data.name;
          this.getList();
        }
      },
      deep: true,
      // immediate: true,
    },
  },
  created() {
    this.getList();
  },
  methods: {
    parseRouteQueryData(data = this.$route.query.data) {
      try {
        // 验证data是否为有效JSON字符串
        if (typeof data !== "string" || !data.trim()) {
          console.warn("Route query data is empty or invalid:", data);
          return {};
        }
        return JSON.parse(data || '{}');
      } catch (error) {
        console.error("Failed to parse route query data:", error);
        return {};
      }
    },
    filterData(value) {
      return !value ? "-" : moneyFormat(value);
    },
    //获取列表
    getList() {
      const { factoryId, accountingMonth, accountingWeek } = this.parseRouteQueryData();
      this.loading = true;
      this.$api.plateTypePieceWageSystem.weekTodoTasks
        .getWeekListWeekSalary({
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          accountingWeek,
          accountingMonth,
          factoryId,
        })
        .then((res) => {
          this.tableData = res.data;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.pageNum = val;
      this.getList();
    },
  },
};
</script>
<style lang="stylus" scoped>
ellipsis() {
  font-size: 16px;
  font-weight: 600;
}

.header_tableName {
  ellipsis();
  font-size: 22px;
  text-align: center;
  margin-bottom: 8px;
}
</style>
