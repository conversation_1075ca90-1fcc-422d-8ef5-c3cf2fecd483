import Layout from "@/layout";
const softwareRouter = [
  {
    path: "/software/workbench",
    component: Layout,
    redirect: "/software/workbench/overview",
    meta: {
      title: "工作台",
    },
    children: [
      {
        name: "SoftwareTask",
        path: "overview",
        component: () =>
          import("@/views-software/overview/agencyTask/agencyTask"),
        meta: {
          title: "待办任务",
        },
      },
      {
        name: "SoftwareTaskDetails",
        path: "taskDetails",
        component: () => import("@/views-software/overview/taskDetails"),
        meta: {
          title: "任务明细",
        },
      },
      {
        name: "softwarePayroll",
        path: "payroll",
        component: () => import("@/views-software/overview/payroll/payroll"),
        meta: {
          title: "工资表",
        },
      },
      {
        name: "SoftwareAdjustment",
        path: "adjustment",
        component: () =>
          import("@/views-software/overview/adjustment/adjustment"),
        meta: {
          title: "分厂调整",
        },
      },
      {
        name: "SoftwareUpload",
        path: "upload",
        component: () => import("@/views-software/overview/upload/upload"),
        meta: {
          title: "个税扣款",
        },
      },
    ],
  },
  {
    path: "/software/account",
    component: Layout,
    redirect: "/software/account/debitLedger",
    meta: {
      title: "信息台账",
    },
    children: [
      {
        name: "SoftwareRemaining",
        path: "remaining",
        component: () =>
          import("@/views-software/account/remaining/ocale/ocale"),
        meta: {
          title: "余留管理",
        },
      },
      {
        name: "SoftwareRemainingDetailed",
        path: "remainingDetailed",
        component: () => import("@/views-software/account/remaining/remaining"),
        meta: {
          title: "余留调整明细",
        },
      },
      {
        name: "SoftwareDebitLedger",
        path: "debitLedger",
        component: () =>
          import("@/views-software/account/debitLedger/debitLedger"),
        meta: {
          title: "借支台账",
        },
      },
      {
        name: "SoftwareDebitList",
        path: "debitList",
        component: () => import("@/views-software/account/debitList"),
        meta: {
          title: "借支清单",
        },
      },
      {
        name: "SoftwareReward",
        path: "reward",
        component: () => import("@/views-software/account/reward/reward"),
        meta: {
          title: "奖惩台账",
        },
      },
      {
        name: "SoftwareRewardList",
        path: "rewardList",
        component: () => import("@/views-software/account/rewardList"),
        meta: {
          title: "奖惩清单",
        },
      },
      {
        name: "SoftwarePayroll",
        path: "payroll",
        component: () =>
          import(
            "@/views-software/account/payrollManagement/payrollManagement"
          ),
        meta: {
          title: "特殊工资单管理",
        },
      },
      {
        name: "SoftwareSpecialPayroll",
        path: "specialPayroll",
        component: () => import("@/views-software/account/specialPayroll"),
        meta: {
          title: "特殊工资单查看",
        },
      },
      {
        name: "softwareCompensation",
        path: "compensation",
        component: () =>
          import("@/views-software/account/costCompensation/costCompensation"),
        meta: {
          title: "成本赔偿扣款台账",
        },
      },
      {
        name: "SoftwareUnpaidLedger",
        path: "unpaidLedger",
        component: () =>
          import("@/views-software/account/unpaidLedger/unpaidLedger"),
        meta: {
          title: "未付款台账",
        },
      },
      {
        name: "SoftwareSalarySearch",
        path: "salarySearch",
        component: () => import("@/views-software/account/salarySearch"),
        meta: {
          title: "工资查询",
        },
      },
      //新员工补贴20%扣款
      {
        name: "SoftwareNewemployeeSubsidy",
        path: "newemployeeSubsidy",
        component: () =>
          import(
            "@/views-software/account/newemployeeSubsidy/newemployeeSubsidy"
          ),
        meta: {
          title: "新员工补贴20%扣款",
        },
      },
      //新/熟手补贴名单管理
      {
        name: "SoftwareNewEmployeeSubsidies",
        path: "newEmployeeSubsidies",
        component: () =>
          import(
            "@/views-software/account/newEmployeeSubsidies/newEmployeeSubsidies"
          ),
        meta: {
          title: "新/熟手补贴名单管理",
        },
      },
    ],
  },
  {
    path: "/software/uploadTasks",
    component: Layout,
    redirect: "/software/uploadTasks/attendance",
    meta: {
      title: "数据上传",
    },
    children: [
      {
        name: "SoftwareAttendance",
        path: "attendance",
        component: () =>
          import("@/views-software/uploadTasks/attendance/attendance"),
        meta: {
          title: "员工考勤",
        },
      },
      {
        name: "SoftwareWage",
        path: "wage",
        component: () => import("@/views-software/uploadTasks/wage/wage"),
        meta: {
          title: "计件工资",
        },
      },
      {
        name: "SoftwareCollective",
        path: "collective",
        component: () => import("@/views-software/uploadTasks/collective"),
        meta: {
          title: "集体账户",
        },
      },
      {
        name: "SoftwareSubsidy",
        path: "subsidy",
        component: () => import("@/views-software/uploadTasks/subsidy/subsidy"),
        meta: {
          title: "其他补贴",
        },
      },
      {
        name: "SoftwareDeduction",
        path: "deduction",
        component: () =>
          import("@/views-software/uploadTasks/deduction/deduction"),
        meta: {
          title: "其他扣款",
        },
      },
      {
        name: "SoftwareSecurity",
        path: "security",
        component: () =>
          import("@/views-software/uploadTasks/security/security"),
        meta: {
          title: "社保扣款",
        },
      },
      {
        name: "SoftwarePuncandeduct",
        path: "puncandeduct",
        component: () =>
          import("@/views-software/uploadTasks/puncandeduct/puncandeduct"),
        meta: {
          title: "未打卡扣款",
        },
      },
      {
        name: "SoftwareUnionfee",
        path: "unionfee",
        component: () =>
          import("@/views-software/uploadTasks/unionfee/unionfee"),
        meta: {
          title: "工会费",
        },
      },
      {
        name: "SoftwareCompanysubsidies",
        path: "companysubsidies",
        component: () =>
          import("@/views-software/uploadTasks/companysubsidies"),
        meta: {
          title: "公司补贴",
        },
      },
      {
        name: "SoftwareHousingsubsidies",
        path: "housingsubsidies",
        component: () =>
          import("@/views-software/uploadTasks/housingsubsidies"),
        meta: {
          title: "住房补贴",
        },
      },
      {
        name: "SoftwareFactoryservicededuction",
        path: "factoryservicededuction",
        component: () =>
          import("@/views-software/uploadTasks/factoryservicededuction"),
        meta: {
          title: "厂服扣款",
        },
      },
      {
        name: "SoftwareFactorycarddeduction",
        path: "factorycarddeduction",
        component: () =>
          import("@/views-software/uploadTasks/factorycarddeduction"),
        meta: {
          title: "厂牌扣款",
        },
      },
      {
        name: "SoftwareLifefei",
        path: "lifefei",
        component: () => import("@/views-software/uploadTasks/lifefei"),
        meta: {
          title: "生活费",
        },
      },
      {
        name: "SoftwarePhysicalexamination",
        path: "physicalexamination",
        component: () =>
          import(
            "@/views-software/uploadTasks/physicalexamination/physicalexamination"
          ),
        meta: {
          title: "体检费",
        },
      },
      {
        name: "SoftwareCostcompensationList",
        path: "costcompensationList",
        component: () =>
          import("@/views-software/uploadTasks/costcompensationList"),
        meta: {
          title: "成本赔偿清单",
        },
      },
      {
        name: "SoftwareCostcompensation",
        path: "costcompensation",
        component: () =>
          import("@/views-software/uploadTasks/costcompensation"),
        meta: {
          title: "成本赔偿",
        },
      },
      {
        name: "SoftwareLowconsumptiongoods",
        path: "lowconsumptiongoods",
        component: () =>
          import("@/views-software/uploadTasks/lowconsumptiongoods"),
        meta: {
          title: "低耗品",
        },
      },
      {
        name: "SoftwareReworkdeduction",
        path: "reworkdeduction",
        component: () => import("@/views-software/uploadTasks/reworkdeduction"),
        meta: {
          title: "返工扣款",
        },
      },
      {
        name: "SoftwareMiscellaneous",
        path: "miscellaneous",
        component: () =>
          import("@/views-software/uploadTasks/miscellaneous/miscellaneous"),
        meta: {
          title: "杂工考勤",
        },
      },
      {
        name: "Softwareenvironmental",
        path: "environmental",
        component: () =>
          import("@/views-software/uploadTasks/environmental/environmental"),
        meta: {
          title: "环境补贴",
        },
      },
      {
        name: "Softwarehightemperature",
        path: "hightemperature",
        component: () =>
          import(
            "@/views-software/uploadTasks/hightemperature/hightemperature"
          ),
        meta: {
          title: "高温补贴",
        },
      },
      {
        name: "Softwaremployeesubsidies",
        path: "mployeesubsidies",
        component: () =>
          import(
            "@/views-software/uploadTasks/mployeesubsidies/mployeesubsidies"
          ),
        meta: {
          title: "新员工补贴",
        },
      },
      {
        name: "Softwareoldhandsubsidy",
        path: "oldhandsubsidy",
        component: () =>
          import("@/views-software/uploadTasks/oldhandsubsidy/oldhandsubsidy"),
        meta: {
          title: "熟手补贴",
        },
      },
      {
        name: "Softwareminimumwage",
        path: "minimumwage",
        component: () =>
          import("@/views-software/uploadTasks/minimumwage/minimumwage"),
        meta: {
          title: "保底工资",
        },
      },
      {
        name: "SoftwareNewemployeeSubsidy",
        path: "newemployeeSubsidy",
        component: () =>
          import(
            "@/views-software/uploadTasks/newemployeeSubsidy/newemployeeSubsidy"
          ),
        meta: {
          title: "新员工补贴20%扣款",
        },
      },
    ],
  },
  {
    path: "/software/system",
    component: Layout,
    redirect: "/software/system/dataconfiguration",
    meta: {
      title: "系统配置",
    },
    children: [
      {
        name: "softwarePageConfig",
        path: "pageconfig",
        component: () => import("@/views-software/system-config/pageConfig"),
        meta: {
          title: "表头管理",
        },
      },
      {
        name: "SoftwareTaskConfig",
        path: "taskconfig",
        component: () =>
          import("@/views-software/system-config/taskConfig/taskConfig"),
        meta: {
          title: "任务配置",
        },
      },
      {
        name: "SoftwareConfiguration",
        path: "dataconfiguration",
        component: () =>
          import(
            "@/views-software/system-config/dataConfiguration/dataConfiguration"
          ),
        meta: {
          title: "数据配置",
        },
      },
      //参数配置
      {
        name: "SoftwareConfiguratask",
        path: "configuratask",
        component: () =>
          import("@/views-software/system-config/confiGuratask/confiGuratask"),
        meta: {
          title: "参数配置",
        },
      },
      //熟手补贴配置
      {
        name: "SoftwareConfioldhand",
        path: "config-oldhand",
        component: () =>
          import(
            "@/views-software/system-config/config-oldhand/config-oldhand"
          ),
        meta: {
          title: "熟手补贴配置",
        },
      },
      //新员工补贴配置
      {
        name: "SoftwareConfinovicesubsidy",
        path: "novice-subsidy",
        component: () =>
          import(
            "@/views-software/system-config/novice-subsidy/config-oldhand"
          ),
        meta: {
          title: "新员工补贴配置",
        },
      },
      //保底补贴配置
      {
        name: "SoftwareConfiminimumwage",
        path: "minimum-wage",
        component: () =>
          import("@/views-software/system-config/minimum-wage/config-oldhand"),
        meta: {
          title: "保底补贴配置",
        },
      },
      //高温补贴配置
      {
        name: "SoftwareConfihightbsidy",
        path: "hight-bsidy",
        component: () =>
          import("@/views-software/system-config/hight-bsidy/config-oldhand"),
        meta: {
          title: "高温补贴配置",
        },
      },
      //环境补贴配置
      {
        name: "SoftwareConficonfiguration",
        path: "config-uration",
        component: () =>
          import(
            "@/views-software/system-config/config-uration/config-uration"
          ),
        meta: {
          title: "环境补贴配置",
        },
      },
      //新员工补贴20%配置
      {
        name: "softwareConfigNewemployee",
        path: "config-newemployee",
        component: () =>
          import(
            "@/views-software/system-config/config-newemployee/newemployee"
          ),
        meta: {
          title: "新员工补贴20%扣款",
        },
      },
    ],
  },
  {
    path: "/software/pieceworkWage",
    component: Layout,
    redirect: "/software/pieceworkWage/pieceworkWageDetails",
    meta: {
      title: "工资管理",
    },
    children: [
      {
        name: "SoftwarePieceworkWageDetails",
        path: "pieceworkWageDetails",
        component: () => import("@/views-software/wage-management/pieceworkWageDetails/pieceworkWageDetails"),
        meta: {
          title: "计件工资查询",
        },
      },
    ],
  },
  {
    path: "/software/manage",
    component: Layout,
    redirect: "/software/manage/employee",
    meta: {
      title: "系统管理",
    },
    children: [
      {
        name: "SoftwareFactoryManagement",
        path: "factoryManagement",
        component: () =>
          import(
            "@/views-software/system-manage/factory-management/factory-management"
          ),
        meta: {
          title: "工厂管理",
        },
      },

      {
        name: "softwareBasicInformation",
        path: "basic",
        component: () =>
          import(
            "@/views-software/system-manage/basic-information/basic-information"
          ),
        meta: {
          title: "班组管理",
        },
      },
      {
        name: "softwareEmployee",
        path: "employee",
        component: () =>
          import("@/views-software/system-manage/employee/employee"),
        meta: {
          title: "员工管理",
        },
      },
      {
        name: "softwareLargeprocess",
        path: "largeprocess",
        component: () => import("@/views-software/system-manage/largeprocess/largeprocess"),
        meta: {
          title: "大工序管理",
        },
      },
      {
        name: "SoftwareDataPermission",
        path: "data",
        component: () =>
          import(
            "@/views-software/system-manage/data-permission/data-permission"
          ),
        meta: {
          title: "数据权限",
        },
      },
      {
        name: "SoftwareSystemLog",
        path: "systemlog",
        component: () => import("@/views-software/system-manage/system-log"),
        meta: {
          title: "系统日志",
        },
      },
    ],
  },
  {
    path: "/software/reportManager",
    component: Layout,
    redirect: "/software/reportManager/summary",
    children: [
      {
        name: "SoftwareSalaryDifferenceWarningForm",
        path: "salaryDifferenceWarningForm",
        component: () => import("@/views-software/reportManager/salaryDifferenceWarningForm/salaryDifferenceWarningForm"),
        meta: {
          title: "工资差异预警表",
        },
      },
      {
        name: "softwareSummary",
        path: "summary",
        component: () => import("@/views-software/reportManager/summary"),
        meta: {
          title: "财务汇总表",
        },
      },
      {
        name: "softwareSalaryAnalysis",
        path: "salaryAnalysis",
        component: () =>
          import("@/views-software/reportManager/salaryAnalysis"),
        meta: {
          title: "工资分析表",
        },
      },
      {
        name: "softwarelaborEfficiency",
        path: "laborEfficiency",
        component: () =>
          import("@/views-software/reportManager/laborEfficiency"),
        meta: {
          title: "人工效率分析表",
        },
      },
    ],
  },
  {
    path: "/software/file",
    component: Layout,
    redirect: "/software/file/import",
    children: [
      {
        name: "SoftwareImportFile",
        path: "import",
        component: () => import("@/views-software/import-export/import-list"),
        meta: {
          title: "导入列表",
        },
      },
      {
        name: "SoftwareExportFile",
        path: "export",
        component: () => import("@/views-software/import-export/export-list"),
        meta: {
          title: "导出列表",
        },
      },
    ],
  },
];
export default softwareRouter;
