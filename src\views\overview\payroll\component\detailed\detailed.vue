<template>
  <content-panel>
    <template v-slot:search>
      <search-box class="search-box">
        <el-form size="mini" :inline="true" :model="searchForm" ref="searchForm">
          <el-form-item label="员工姓名:" prop="staffName">
            <el-input clearable v-model.trim="searchForm.staffName" size="mini" @keyup.enter.native="onSearch">
              <template slot="append">
                <search-batch @seachFilter="onSearch('staffNames', $event)"
                  @focusEvent="focusEvent('staffNames', $event)" ref="childrenStaffNames" titleName="员工姓名" />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="厂牌编号:" prop="staffCode">
            <el-input v-model.trim="searchForm.staffCode" size="mini" clearable @keyup.enter.native="onSearch">
              <template slot="append">
                <search-batch @seachFilter="onSearch('staffCodes', $event)"
                  @focusEvent="focusEvent('staffCodes', $event)" ref="childrenStaffCodes" titleName="厂牌编号" />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="核算班组:" prop="processId">
            <el-select clearable v-model.trim="searchForm.processId" placeholder="请选择" @change="onSearch">
              <el-option v-for="item in teamOptinon" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="异常类型:" prop="invalidSearch">
            <el-select @change="onSearch" clearable v-model="searchForm.invalidSearch" placeholder="请选择">
              <el-option v-for="item in invalidOptions" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="备注二:" prop="remark">
            <el-select v-model="searchForm.remark" placeholder="请选择" @change="onSearch">
              <el-option v-for="item in remarkTypeList" :key="item.value" :label="item.name" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button size="small" type="primary" @click="onSearch">
            查询
          </el-button>
          <el-button size="small" type="warning" @click="resetSearchForm">
            重置
          </el-button>
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <template v-slot:header-left>
        <div style="color: #0bb78e" v-show="info.completeState == '0'">
          {{
            `未审核:${statusInfo.notAudit || 0} 审核中:${statusInfo.auditing || 0
            }`
          }}
        </div>
      </template>
      <!-- 隐藏金额为0的列 -->
      <template v-slot:header-right>
        <el-checkbox style="color: #0bb78e" v-model="hiddenAmount"
          @change="changeHiddenAmount">导出时,隐藏金额为0的列</el-checkbox>
      </template>
      <vxe-table :key="tableKey" ref="tableRef" resizable stripe border :loading="loading" :loading-config="{
        icon: 'vxe-icon-indicator roll',
        text: '正在拼命加载中...',
      }" highlight-hover-row show-header-overflow="tooltip" show-overflow="tooltip" :height="maxTableHeight"
        :data="tableData" :checkbox-config="checkboxConfig" @checkbox-all="handleSelectionAll"
        @checkbox-change="handleSelectionChange">
        <template v-if="isTableShow">
          <vxe-column type="checkbox" width="60" fixed="left"></vxe-column>
          <vxe-column v-for="column in columnList" :key="column.fieldName" :field="column.fieldName"
            :title="column.columnName" :width="column.width" :fixed="column.fixed" show-overflow>
            <template slot="header" v-if="column.columnName == '日均工资'">
              <span>日均工资</span>
              <el-tooltip content="日均工资=该厂该月核算班组的总应发工资/该厂该月核算班组的总出勤天数" placement="top">
                <i class="el-icon-question"></i>
              </el-tooltip>
            </template>
            <template slot-scope="{ row }">
              <span v-if="!column.type">
                <div v-if="column.fieldType == 'double'">
                  <span v-if="
                    column.fieldName == 'leftover' &&
                    filterIncomingSalary(row[column.fieldName])
                  " style="color: red">
                    {{ filterDouble(row[column.fieldName]) }}
                  </span>
                  <span v-else> {{ filterDouble(row[column.fieldName]) }}</span>
                </div>
                <span v-else-if="
                  column.fieldName == 'remark2' &&
                  column.fieldType != 'double'
                ">{{ filterData(row.markStatus, row.remark2) }}</span>
                <span v-else-if="list.includes(column.fieldName)">{{
                  !row[column.fieldName] ? "-" : row[column.fieldName]
                }}</span>
                <div v-else-if="
                  ['dailyDiffRatio', 'dailyPieceWageDiffRatio', 'actualDailyDiffRatio'].includes(
                    column.fieldName
                  )
                ">
                  <span v-if="
                    row[column.fieldName] == 'NA' ||
                    parseFloat(row[column.fieldName]) > '20'
                  " style="color: red">{{ row[column.fieldName] }}</span>
                  <span v-else>{{
                    ["0%", "0.00%"].includes(row[column.fieldName]) ||
                      row[column.fieldName] == null
                      ? "-"
                      : row[column.fieldName]
                  }}</span>
                </div>
                <span v-else>{{ row[column.fieldName] }}</span>
              </span>
              <el-button v-else v-show="permission" v-permission="'was-customized$workBench$customized$workOverview$payrollEdit'
                " type="text" @click="procedureEdit(row)">
                编辑
              </el-button>
            </template>
            <template slot="header" v-if="
              [
                'dailyWage',
                'processActualDailyWage',
                'personDailyWage',
                'personActualDailyWage',
                'dailyDiffRatio',
                'actualDailyDiffRatio',
                'dailyPieceWage',
                'dailyPieceWageDiffRatio',
                'personDailyPieceWage',
              ].includes(column.fieldName)
            ">
              <span>{{ items[column.fieldName].name }}</span>
              <el-tooltip v-if="items[column.fieldName].name" :content="items[column.fieldName].tips" placement="top">
                <i class="el-icon-question"></i>
              </el-tooltip>
            </template>
          </vxe-column>
        </template>
      </vxe-table>
      <template v-slot:footer>
        <div class="table_footer">
          <ul>
            <li>
              <span>核算工厂:</span><span>{{ info.factoryName }}</span>
            </li>
            <li>
              <span>核算月份:</span><span>{{ info.accountingMonth }}</span>
            </li>
            <li>
              <span>人数:</span><span>{{ statiStics.people }}</span>
            </li>
            <li>
              <span>补贴+系统计件:</span><span>{{ statiStics.pieceWage | moneyFormat }}</span>
            </li>
            <li>
              <span>应发总工资:</span><span>{{ statiStics.salary | moneyFormat }}</span>
            </li>
            <li>
              <span>实发总工资:</span><span>{{ statiStics.actualSalary | moneyFormat }}</span>
            </li>
          </ul>
        </div>
        <div style="text-align: right">
          <el-pagination @size-change="onSizeChange" @current-change="onNumChange" :current-page="pageNum"
            :page-size="pageSize" :total="total" :page-sizes="[50, 100, 200]"
            layout="total, sizes, prev, pager, next, jumper">
          </el-pagination>
        </div>
      </template>
    </table-panel>
    <custom-dialog v-if="visible" :visible="visible" @cancel="handleCancel"></custom-dialog>
    <common-dialog v-if="isVisible" :visible="isVisible" :title="title" :formData="formData" @cancel="commonCancel">
    </common-dialog>
    <custom-config v-if="salaryVisible" @success="hanleSuccess" :visible.sync="salaryVisible"></custom-config>

    <print-dialog v-if="printVisible" :visible="printVisible" :searchForm="filterParam" :params="params"
      @cancel="printCancel" :rowList="tableData">
    </print-dialog>
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import customDialog from "./compontent/customDialog";
import CommonDialog from "./compontent/commonDialog";
import printDialog from "./compontent/printDialog";
import customConfig from "./compontent/custom-config";
import { calculateTableWidth } from "@/utils";
import { moneyFormat, moneyDelete } from "@/utils";
import { exceptionEnum } from '@/utils/constant';
//当前场景唯一键名
const SCEN_NAME = '定制工资表';
export default {
  name: "PayrollDetails",
  mixins: [tableMixin],
  components: { customDialog, CommonDialog, customConfig, printDialog },
  data() {
    return {
      searchForm: {
        invalidSearch: "",
        processId: "",
        staffCode: "",
        staffName: "",
        remark: "",
      },
      hiddenAmount: true,
      procedureRules: {
        procedure: [
          { message: "工序不能为空", trigger: "change", required: true },
        ],
      },
      title: "",
      visible: false,
      salaryVisible: false,
      isVisible: false,
      formData: {},
      tableData: [],
      invalidOptions: exceptionEnum,
      processOptions: [
        { name: "是", id: "Y" },
        { name: "否", id: "N" },
      ],
      remarkTypeList: Object.freeze([
        {
          name: "全部",
          value: "",
        },
        {
          name: "正常",
          value: "0",
        },
        {
          name: "辞职上卡",
          value: "11",
        },
        {
          name: "自离",
          value: "22",
        },
        {
          name: "延发",
          value: "3",
        },
        {
          name: "线下已结",
          value: "4",
        },
        {
          name: "其他（暂不发放）",
          value: "5",
        },
        {
          name: "其他",
          value: "6",
        },
        {
          name: "已结算",
          value: "7",
        },
        {
          name: "辞职领现",
          value: "12",
        },
        {
          name: "自离上卡",
          value: "21",
        },
      ]),
      factoryId: "",
      loading: false,
      filterParam: {},
      //全部表格头
      columnList: [],
      list: Object.freeze([
        "totalWorkDay",
        "totalWorkHour",
        "workdayWorkDay",
        "workdayWorkHour",
        "nightOvertimeHour",
        "weekendWork",
        "holidayWork",
        "totalWorkHourWithOvertime",
        "workHour",
      ]),
      pageSize: 50,
      pageNum: 1,
      total: 0,
      resizeOffset: 68,
      params: {},
      isTableShow: true,
      statiStics: {},
      permission: "",
      info: {},
      idList: [],
      items: Object.freeze({
        dailyWage: {
          name: "工序日均应发",
          tips: "工序日均应发=该厂该月核算班组的总应发工资/该厂该月核算班组的（总出勤天数+杂工加班小时/8）",
        },
        processActualDailyWage: {
          name: "工序日均实发",
          tips: "工序日均实发=该厂该月核算班组的总实发工资/该厂该月核算班组的（总出勤天数+杂工加班小时/8）",
        },
        dailyPieceWage: {
          name: "工序日均计件",
          tips: "工序日均计件=该工序的总【调整后系统计件总工资】/本厂出勤天数",
        },
        personDailyWage: {
          name: "个人日均应发",
          tips: "个人日均应发=该身份证的应发工资/该厂该月核算班组的（总出勤天数+杂工加班小时/8）",
        },
        personActualDailyWage: {
          name: "个人日均实发",
          tips: "个人日均实发=该身份证的实发工资/该厂该月核算班组的（总出勤天数+杂工加班小时/8）",
        },
        personDailyPieceWage: {
          name: "个人日均计件",
          tips: "个人日均计件=该身份证的【调整后系统计件总工资】之和/该身份证的本厂出勤天数",
        },
        dailyDiffRatio: {
          name: "日均应发差异率",
          tips: "（个人日均应发-工序日均应发）/工序日均应发*100%，保留两位小数（四舍五入）",
        },
        actualDailyDiffRatio: {
          name: "日均实发差异率",
          tips: "（个人日均实发-工序日均实发）/工序日均实发*100%，保留两位小数（四舍五入）",
        },
        dailyPieceWageDiffRatio: {
          name: "日均计件差异率",
          tips: "（个人日均计件-工序日均计件）/工序日均计件*100%，保留两位小数（四舍五入）",
        },
      }),
      statusInfo: {},
      tableKey: 0,
      printVisible: false,
      checkboxConfig: {
        checkMethod({ row }) {
          // 根据条件判断是否禁用复选框
          return row.markStatus != "已结算";
        },
      },
      teamOptinon: [],
    };
  },
  created() { },
  computed: {
    tableColumn() {
      this.columnList = this.columnList.map((item) => ({
        ...item,
        isShow: item.type ? this.permission : true,
      }));
      return this.columnList.filter((item) => item.isShow);
    },
  },
  watch: {
    $route: {
      handler(value) {
        if (
          value.path.includes("payroll") &&
          value.path.includes("customized")
        ) {
          this.info = this.parseRouteQueryData();
          this.factoryId = this.info.factoryId;
          this.getStatusTotal();
          this.getAvailableGroups();
          this.getList();
          this.getDebitList();
          this.TaskPermissionrSubsidy();
        }
      },
      immediate: true,
      deep: true,
    },
  },
  mounted() {
    this.$bus.$off("customizedCustomColumns", () => {
      this.salaryVisible = false;
    });
    this.$bus.$on("customizedCustomColumns", () => {
      this.salaryVisible = true;
    });
    this.$bus.$off("customizedBatchRemarks");
    this.$bus.$on("customizedBatchRemarks", this.batchRemark);
    this.$bus.$off("customizedPrintSettings");
    this.$bus.$on("customizedPrintSettings", this.printSettings);
  },
  methods: {
    parseRouteQueryData(data = this.$route.query.data) {
      try {
        // 验证data是否为有效JSON字符串
        if (typeof data !== "string" || !data.trim()) {
          console.warn("Route query data is empty or invalid:", data);
          return {};
        }
        return JSON.parse(data);
      } catch (error) {
        console.error("Failed to parse route query data:", error);
        return {};
      }
    },
    changeHiddenAmount(val) {
      this.$emit('getStatus', val);
      // console.log('val',val)
    },
    // 表头颜色
    tableHeaderColor({ row, column, rowIndex, columnIndex }) {
      if (
        (rowIndex === 0 && column.label === "应发工资") ||
        (rowIndex === 0 && column.label === "实发工资")
      ) {
        return "color:green;";
      }
    },
    cellStyle({ row, column, rowIndex, columnIndex }) {
      if (column.label === "实发工资" || column.label === "应发工资") {
        return "color:green;";
      }
    },
    // 获取历史任务操作权限
    TaskPermissionrSubsidy() {
      let params = {
        accountingMonth: this.info.accountingMonth,
        factoryId: this.factoryId,
      };
      this.$api.statiStics
        .statisticsOthegetCompleteTaskPermissionrSubsidy(params)
        .then((res) => {
          this.permission = res.data;
        });
    },
    getDebitList() {
      let params = {
        accountingMonth: this.info.accountingMonth,
        factoryId: this.factoryId,
        ...this.filterParam,
      };
      this.$api.statiStics.statisticsSalary(params).then((res) => {
        if (res.code === 200) {
          this.statiStics = res.data
            ? res.data
            : {
              people: 0,
              pieceWage: 0,
              salary: 0,
              actualSalary: 0,
            };
        }
      });
    },
    filterData(value, remark) {
      if (!["其他（暂不发放）", "其他", "正常"].includes(value)) return value;
      if (remark) {
        if (value == "其他（暂不发放）") {
          return `${value}-${remark}`;
        } else {
          return remark;
        }
      } else {
        if (value == "其他（暂不发放）") {
          return `${value}`;
        } else {
          return "";
        }
      }
    },
    filterDouble(value) {
      if (!value) return "-";
      return moneyFormat(value);
    },
    filterIncomingSalary(value) {
      return value && moneyDelete(value) > 3000;
    },
    checCheckboxkMethod({ row }) {
      return row.markStatus == "已结算";
    },
    //状态统计
    getStatusTotal() {
      this.$api.workbench
        .specialStatistics({
          factoryId: this.factoryId,
          accountingMonth: this.info.accountingMonth,
        })
        .then(({ data }) => {
          this.statusInfo = data || {};
        });
    },
    async hanleSuccess() {
      this.getList();
      this.getDebitList();
    },
    //获取工资表明细列表
    async getList() {
      this.loading = true;
      const { data } = await this.$api.workbench.sceneConfigurationList("SalarySheetDetail"); 
 
      const scenList = data.filter((item) => item.name === SCEN_NAME);
      const scenInfo = scenList && scenList[0] || {};
      let scenInfoId = scenInfo.id || "";
       if(scenList.length &&  !(scenInfo&&scenInfo.columns&&scenInfo.columns.length)) {
        await this.$api.workbench.sceneConfigurationDelete({ id:scenInfoId })
      } 
      if (!scenList.length) {
        await this.$api.workbench.sceneConfigurationAdd({
            moduleType: "SalarySheetDetail",
            name: SCEN_NAME,
        });
        const { data } = await this.$api.workbench.sceneConfigurationList("SalarySheetDetail");
        const list = data.filter((item) => item.name === SCEN_NAME);  
        await this.$api.workbench.addColumns({
            sceneId: list[0].id || "",
            columns: data[0].columns.map(item => ({
              columnName: item.columnName,
              fieldName: item.fieldName,
            }))
          });
        scenInfoId = list[0].id;
      } 
 
      this.$api.workbench
        .lookSalary({
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          filterData: {
            factoryId: this.factoryId,
            sceneId: scenInfoId,
            accountingMonth: this.info.accountingMonth,
            ...this.filterParam,
            ...this.params,
          },
        })
        .then(
          ({
            data: {
              columnVOs,
              pageInfo: { list, total },
            },
          }) => {
            this.isTableShow = false;
            this.$nextTick(() => {
              const width = document.querySelector(".vxe-table").clientWidth;
              this.tableData = list || [];
              let items = {
                serialNumber: "60",
                staffName: "100",
                staffCode: "120",
                idCard: "140",
                operation: "100",
              };
              this.columnList = columnVOs
                .concat([
                  {
                    columnName: "",
                    fieldName: "operation",
                    type: "operation",
                    fixed: "right",
                  },
                ])
                .map((item) => {
                  if (Object.keys(items).includes(item.fieldName)) {
                    Object.keys(items).forEach((key) => {
                      if (key == item.fieldName) {
                        item.width = items[item.fieldName];
                      }
                    });
                  } else {
                    item.width = this.flexWidth(
                      item.fieldName,
                      this.tableData,
                      item.columnName
                    );
                  }
                  if (
                    ["序号", "员工姓名", "厂牌编号"].includes(item.columnName)
                  ) {
                    item.fixed = "left";
                  }
                  return item;
                });
              let totalWidth = this.columnList.reduce((pre, cur) => {
                return (pre += Number(cur.width));
              }, 0);
              if (totalWidth <= width) {
                this.columnList.forEach((item) => {
                  delete item.width;
                });
              }
              this.total = total;
              this.isTableShow = true;
              this.tableKey = Math.random();
            });
          }
        )
        .finally(() => {
          this.loading = false;
        });
    },
    //根据工厂和核算月份查询班组列表
    getAvailableGroups() {
      this.$api.workbench
        .availableGroups({
          factoryId: this.factoryId,
          accountingMonth: this.info.accountingMonth,
        })
        .then(({ data }) => {
          this.teamOptinon = data || [];
        });
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.$refs.childrenStaffNames.resetFilter();
      this.$refs.childrenStaffCodes.resetFilter();
      this.filterParam = {};
      this.params = {};
      this.onSearch();
    },
    //搜索
    onSearch(name, data) {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0)
          this.filterParam[name] = data;
      }
      this.pageNum = 1;
      this.getList();
      this.getDebitList();
    },
    focusEvent(name, data) {
      if (Array.isArray(data) && !data.length) {
        this.params[name] = [];
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.params[name] = data;
      }
    },
    //计算表格列宽度
    flexWidth(...args) {
      return calculateTableWidth(...args);
    },
    //批量备注
    batchRemark() {
      if (this.idList.length < 1) {
        this.$message.warning("请至少勾选一条数据");
        return;
      }
      this.title = "批量备注";
      this.isVisible = true;
      this.formData = {
        idList: this.idList,
      };
    },
    //编辑
    procedureEdit(row) {
      this.title = "编辑";
      this.isVisible = true;
      this.formData = {
        params: {
          id: row.id || "",
          staffCode: row.staffCode || "",
          remark: row.remark2 || "",
          markStatus: row.markStatus,
        },
        procedure: row.accountingProcess || "",
      };
    },
    //打印
    printSettings() {
      this.printVisible = true;
    },
    handleCancel(type) {
      this.visible = false;
      if (type == "cancel") return;
      this.getList();
      this.getDebitList();
    },
    commonCancel(type) {
      this.isVisible = false;
      if (type == "cancel") return;
      this.idList = [];
      this.getList();
    },
    printCancel(type) {
      this.printVisible = false;
    },
    handleSelectionChange({ row, checked }) {
      if (checked) {
        this.idList.push(row.id);
      } else {
        let findIndex = this.idList.findIndex((item) => item == row.id);
        this.idList.splice(findIndex, 1);
      }
    },
    handleSelectionAll({ checked }) {
      if (checked) {
        this.tableData.forEach((item) => {
          if (!this.idList.includes(item.id)) this.idList.push(item.id);
        });
      } else {
        this.idList = [];
      }
    },
    onSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.pageNum = 1;
      this.getList();
    },
    onNumChange(pageNum) {
      this.pageNum = pageNum;
      this.getList();
    },
  },
};
</script>

<style lang="stylus" scoped>
>>> .el-form--inline .el-form-item {
  margin-right: 40px;
}
.el-form--label-left {
  .el-form-item {
    margin-right: 16px;

    &.third {
      >>>.el-form-item__content {
        margin: 0 !important;
      }
    }
  }
}

.table-panel {
  >>>.table-panel-header {
    .table-panel-header-right {
      display: flex;
      justify-content: space-between;

      .pageRight {
        margin-right: 10px;
      }
    }
  }
}

>>>.table-panel-footer-top {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .table_footer {
    overflow: auto;

    >ul {
      display: flex;
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        white-space: nowrap
        padding: 0 10px;
      }
    }
  }
}
.procedure {
  >>>.el-form-item__content {
    .el-select {
      width: 100%;
    }
  }
}

>>>.el-tabs__nav-wrap::after {
  display: block;
  height: 0;
}
>>>.el-textarea__inner {
  padding-right: 50px;
}

.el-textarea {
  >>>.el-input__count {
    right: 20px !important;

  }
}
</style>
