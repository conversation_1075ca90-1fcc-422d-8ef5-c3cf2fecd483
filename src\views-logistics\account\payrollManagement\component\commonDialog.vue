<template>
  <qDialog
    :visible="commonVisible"
    :innerScroll="innerScroll"
    :innerHeight="height"
    :showFooter="showFooter"
    :isShowCancelBtn="isShowCancelBtn"
    :title="commonTitle"
    :width="width"
    :modal-append-to-body="false"
    append-to-body
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel"
  >
    <div v-if="commonTitle == '打印预览'">
      <div class="btn">
        <el-button
          size="small"
          type="primary"
          @click="handlePrint"
          style="margin-bottom: 10px"
        >
          打印
        </el-button>
      </div>
      <section>
        <div class="header">
          <div class="header_top">
            <div>
              <span>工资单类型:</span>
              <span>{{ name }}</span>
            </div>
            <h3 style="letter-spacing: 10px">工资结算单</h3>
          </div>
          <div class="header_bottom">
            <div class="item">
              <span>员工编号:{{ printInfo.staffCode }}</span>
            </div>
            <div class="item">
              <span>备注:{{ printInfo.remark }}</span>
            </div>
            <div class="item">
              <span>部门:{{ printInfo.factoryName }}</span>
            </div>
            <div class="item">
              <span>服务对象:</span>
            </div>
            <div class="item">
              <span>服务公司:</span>
            </div>
            <div class="item">
              <span>职务:{{ printInfo.position }}</span>
            </div>
            <div class="item">
              <span>姓名:{{ printInfo.staffName }}</span>
            </div>
          </div>
        </div>
        <el-table stripe border ref="tableRef" :height="140" :data="tableData">
          <el-table-column
            prop="accountingMonth"
            label="年/月"
            width="75"
            align="left"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            label="津贴项目"
            align="center"
            show-overflow-tooltip
          >
            <el-table-column
              label="工资总额"
              prop="totalWage"
              align="left"
              show-overflow-tooltip
            >
              <template slot-scope="{ row }">
                <span
                  :style="{
                    color:
                      filterData(row.totalWage.sysAmount) ==
                      filterData(row.totalWage.settlementAmount)
                        ? '#0BB78E'
                        : 'red',
                  }"
                >
                  {{ row.totalWage.settlementAmount | moneyFormat }}</span
                >
              </template>
            </el-table-column>
            <el-table-column
              label="奖励"
              prop="rewards"
              align="left"
              show-overflow-tooltip
            >
              <template slot-scope="{ row }">
                <span
                  :style="{
                    color:
                      filterData(row.rewards.sysAmount) ==
                      filterData(row.rewards.settlementAmount)
                        ? '#0BB78E'
                        : 'red',
                  }"
                >
                  {{ row.rewards.settlementAmount | moneyFormat }}</span
                >
              </template>
            </el-table-column>
            <el-table-column
              label="补贴"
              prop="subsidy"
              align="left"
              show-overflow-tooltip
            >
              <template slot-scope="{ row }">
                <span
                  :style="{
                    color:
                      filterData(row.subsidy.sysAmount) ==
                      filterData(row.subsidy.settlementAmount)
                        ? '#0BB78E'
                        : 'red',
                  }"
                >
                  {{ row.subsidy.settlementAmount | moneyFormat }}</span
                >
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column
            :label="`应得工资\n总额`"
            prop="salary"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              <span
                :style="{
                  color:
                    filterData(row.salary.sysAmount) ==
                    filterData(row.salary.settlementAmount)
                      ? '#0BB78E'
                      : 'red',
                }"
              >
                {{ row.salary.settlementAmount | moneyFormat }}</span
              >
            </template>
          </el-table-column>
          <el-table-column
            label="扣款项目"
            align="center"
            show-overflow-tooltip
          >
            <el-table-column
              label="处罚"
              prop="punishment"
              align="left"
              show-overflow-tooltip
            >
              <template slot-scope="{ row }">
                <span
                  :style="{
                    color:
                      filterData(row.punishment.sysAmount) ==
                      filterData(row.punishment.settlementAmount)
                        ? '#0BB78E'
                        : 'red',
                  }"
                >
                  {{ row.punishment.settlementAmount | moneyFormat }}</span
                >
              </template>
            </el-table-column>
            <el-table-column
              label="成本赔偿"
              prop="costCompensation"
              align="left"
              show-overflow-tooltip
            >
              <template slot-scope="{ row }">
                <span
                  :style="{
                    color:
                      filterData(row.costCompensation.sysAmount) ==
                      filterData(row.costCompensation.settlementAmount)
                        ? '#0BB78E'
                        : 'red',
                  }"
                >
                  {{
                    row.costCompensation.settlementAmount | moneyFormat
                  }}</span
                >
              </template>
            </el-table-column>
            <el-table-column
              label="低耗扣款"
              prop="lowCostDeduction"
              align="left"
              show-overflow-tooltip
            >
              <template slot-scope="{ row }">
                <span
                  :style="{
                    color:
                      filterData(row.lowCostDeduction.sysAmount) ==
                      filterData(row.lowCostDeduction.settlementAmount)
                        ? '#0BB78E'
                        : 'red',
                  }"
                >
                  {{
                    row.lowCostDeduction.settlementAmount | moneyFormat
                  }}</span
                >
              </template>
            </el-table-column>
            <el-table-column
              label="平板扣款"
              prop="tabletDeduction"
              align="left"
              show-overflow-tooltip
            >
              <template slot-scope="{ row }">
                <span
                  :style="{
                    color:
                      filterData(row.tabletDeduction.sysAmount) ==
                      filterData(row.tabletDeduction.settlementAmount)
                        ? '#0BB78E'
                        : 'red',
                  }"
                >
                  {{ row.tabletDeduction.settlementAmount | moneyFormat }}</span
                >
              </template>
            </el-table-column>
            <el-table-column
              label="工会费"
              prop="unionFees"
              align="left"
              show-overflow-tooltip
            >
              <template slot-scope="{ row }">
                <span
                  :style="{
                    color:
                      filterData(row.unionFees.sysAmount) ==
                      filterData(row.unionFees.settlementAmount)
                        ? '#0BB78E'
                        : 'red',
                  }"
                >
                  {{ row.unionFees.settlementAmount | moneyFormat }}</span
                >
              </template>
            </el-table-column>
            <el-table-column
              label="社保"
              prop="socialSecurity"
              align="left"
              show-overflow-tooltip
            >
              <template slot-scope="{ row }">
                <span
                  :style="{
                    color:
                      filterData(row.socialSecurity.sysAmount) ==
                      filterData(row.socialSecurity.settlementAmount)
                        ? '#0BB78E'
                        : 'red',
                  }"
                >
                  {{ row.socialSecurity.settlementAmount | moneyFormat }}</span
                >
              </template>
            </el-table-column>
            <el-table-column
              label="公积金"
              prop="reserveFund"
              align="left"
              show-overflow-tooltip
            >
              <template slot-scope="{ row }">
                <span
                  :style="{
                    color:
                      filterData(row.reserveFund.sysAmount) ==
                      filterData(row.reserveFund.settlementAmount)
                        ? '#0BB78E'
                        : 'red',
                  }"
                >
                  {{ row.reserveFund.settlementAmount | moneyFormat }}</span
                >
              </template>
            </el-table-column>
            <el-table-column
              label="考勤扣款"
              prop="attendanceDeduction"
              align="left"
              show-overflow-tooltip
            >
              <template slot-scope="{ row }">
                <span
                  :style="{
                    color:
                      filterData(row.attendanceDeduction.sysAmount) ==
                      filterData(row.attendanceDeduction.settlementAmount)
                        ? '#0BB78E'
                        : 'red',
                  }"
                >
                  {{
                    row.attendanceDeduction.settlementAmount | moneyFormat
                  }}</span
                >
              </template>
            </el-table-column>
            <el-table-column
              label="工装扣款"
              prop="uniformDeduction"
              align="left"
              show-overflow-tooltip
            >
              <template slot-scope="{ row }">
                <span
                  :style="{
                    color:
                      filterData(row.uniformDeduction.sysAmount) ==
                      filterData(row.uniformDeduction.settlementAmount)
                        ? '#0BB78E'
                        : 'red',
                  }"
                >
                  {{
                    row.uniformDeduction.settlementAmount | moneyFormat
                  }}</span
                >
              </template>
            </el-table-column>
            <el-table-column
              label="个人所得税"
              prop="individualIncomeTax"
              align="left"
              width="100"
              show-overflow-tooltip
            >
              <template slot-scope="{ row }">
                <span
                  :style="{
                    color:
                      filterData(row.individualIncomeTax.sysAmount) ==
                      filterData(row.individualIncomeTax.settlementAmount)
                        ? '#0BB78E'
                        : 'red',
                  }"
                >
                  {{
                    row.individualIncomeTax.settlementAmount | moneyFormat
                  }}</span
                >
              </template>
            </el-table-column>
            <el-table-column
              label="自离扣款"
              prop="leaveDeduct"
              align="left"
              show-overflow-tooltip
            >
              <template slot-scope="{ row }">
                <span
                  :style="{
                    color:
                      filterData(row.leaveDeduct.sysAmount) ==
                      filterData(row.leaveDeduct.settlementAmount)
                        ? '#0BB78E'
                        : 'red',
                  }"
                >
                  {{ row.leaveDeduct.settlementAmount | moneyFormat }}</span
                >
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column
            :label="`实发工资\n(元)`"
            prop="actualSalary"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              <span
                :style="{
                  color:
                    filterData(row.actualSalary.sysAmount) ==
                    filterData(row.actualSalary.settlementAmount)
                      ? '#0BB78E'
                      : 'red',
                }"
              >
                {{ row.actualSalary.settlementAmount | moneyFormat }}</span
              >
            </template>
          </el-table-column>
          <el-table-column label="员工签名"> </el-table-column>
        </el-table>
        <div class="table-footer">
          <div id="table_footer">
            <!-- <div
              class="footer_left">
              <span>合计人民币（小写）:</span>
              <span>{{smallNum==0?'-':smallNum}}</span>
            </div> -->
            <div class="footer_left">
              <span>合计人民币（大写）:</span>
              <span>{{ smallNum == 0 ? "-" : bigNum }}</span>
            </div>
          </div>

          <div class="header_bottom" style="margin-top: 10px">
            <div class="item">
              <span>财务部审核:</span>
              <span></span>
            </div>
            <div class="item">
              <span>财务复审:</span>
              <span></span>
            </div>
            <div class="item">
              <span>人资复审:</span>
              <span></span>
            </div>
            <div class="item">
              <span>制表:{{ printInfo.tableName }}</span>
              <span></span>
            </div>
            <div class="item">
              <span>结算日期:</span>
              <span>{{ printInfo.handleTime | shortDate }}</span>
            </div>
          </div>
        </div>
        <!-- <div class="footer">
          <div class="item">
            <span>打印次数:</span>
            <span>{{printInfo.printCount}}</span>
          </div>
          <div class="item">
            <span>页码:</span>
            <span>{{size}}</span>
          </div>
        </div> -->
      </section>
      <div style="text-align: right">
        <el-pagination
          @current-change="onNumChange"
          :current-page="pageNum"
          :page-size="page"
          :total="total"
          layout="total, prev, pager, next, jumper"
        >
        </el-pagination>
      </div>
    </div>
    <div v-if="commonTitle == '批量提交'">是否批量提交？</div>
    <div v-if="commonTitle == '批量审核'">是否批量审核并打印？</div>
    <div v-if="commonTitle == '作废'">
      <el-form
        ref="cancelForm"
        :model="cancelForm"
        :rules="cancelRules"
        label-width="150px"
      >
        <el-form-item
          class="cancelForm"
          label="再次输入,确认作废:"
          prop="cancel"
        >
          <el-input v-model="cancelForm.cancel" placeholder="作废"> </el-input>
        </el-form-item>
      </el-form>
    </div>
    <div v-if="commonTitle == '提交'">是否提交？</div>
    <div v-if="commonTitle == '通过'">是否通过并打印？</div>
    <div v-if="commonTitle == '退回'">
      <el-form
        ref="reasonForm"
        :model="reasonForm"
        :rules="reasonRules"
        label-width="100px"
      >
        <el-form-item label="退回原因:" prop="reason">
          <el-input
            style="width: 100%"
            type="textarea"
            resize="none"
            rows="3"
            maxlength="300"
            show-word-limit
            v-model="reasonForm.reason"
            placeholder="请输入退回原因"
          >
          </el-input>
        </el-form-item>
      </el-form>
    </div>
    <div v-if="commonTitle == '退回原因'" class="reason">
      <div class="item">
        <span>退回人员:</span>
        <span>{{ reasonInfo.backName }}</span>
      </div>
      <div class="item">
        <span>退回原因:</span>
        <span>{{ reasonInfo.backReason }}</span>
      </div>
    </div>
  </qDialog>
</template>

<script>
import { moneyFormat, moneyDelete } from "@/utils";
import { getSum, dealBigMoney } from "../common";
import { formatNumber } from "@/utils";
import { soket } from "./soket";
export default {
  props: {
    commonVisible: {
      type: Boolean,
      required: true,
    },

    commonTitle: {
      type: String,
      required: true,
    },
    info: [Object, Array],
  },
  data() {
    return {
      width: "",
      height: 0,
      page: 4,
      size: "",
      pageNum: 1,
      total: 0,
      showFooter: false,
      innerScroll: false,
      isShowCancelBtn: false,
      printInfo: {},
      tableData: [],
      tableList: [],
      //工资条类型
      payrollOptions: Object.freeze([
        {
          name: "辞职工资",
          value: "1",
        },
        {
          name: "自离工资",
          value: "2",
        },
        {
          name: "延发工资",
          value: "3",
        },
        {
          name: "其他工资",
          value: "4",
        },
      ]),
      reasonRules: {
        reason: {
          required: true,
          message: "退回原因不能为空",
          trigger: "blur",
        },
      },
      cancelRules: {
        cancel: [
          { required: true, message: "请输入作废", trigger: "blur" },
          {
            validator: (rule, value, callback) => {
              if (value != "作废") {
                callback(new Error("输入内容只能为作废字段"));
              }
              callback();
            },
            trigger: "blur",
          },
        ],
      },
      smallNum: "",
      bigNum: "",
      reasonForm: {
        reason: "",
      },
      cancelForm: {
        cancel: "",
      },
      pageSize: "",
      name: "",
      reasonInfo: {
        backName: "",
        backReason: "",
      },
      isLoading: false,
    };
  },
  watch: {
    commonTitle: {
      handler(value) {
        switch (value) {
          case "打印预览":
            this.width = "1000px";
            this.height = 500;
            this.showFooter = false;
            this.innerScroll = true;
            this.getDetail();
            break;
          case "批量提交":
          case "批量审核":
          case "作废":
          case "提交":
          case "退回":
          case "通过":
            this.width = "400px";
            this.height = 300;
            this.showFooter = true;
            this.isShowCancelBtn = true;
            this.innerScroll = false;
            break;
          case "退回原因":
            this.width = "400px";
            this.height = 300;
            this.showFooter = true;
            this.isShowCancelBtn = false;
            this.innerScroll = false;
            break;
          default:
            this.width = "900px";
            this.row = 8;
            this.height = 400;
            break;
        }
      },
      immediate: true,
    },
    tableData: {
      handler(value) {
        if (value.length > 0) {
          if (this.commonTitle == "打印预览") {
            // this.$emit('table', value)
            this.smallNum = moneyFormat(getSum(value));
            this.bigNum = dealBigMoney(this.smallNum);
          }
        }
      },
      deep: true,
      immediate: true,
    },
  },
  created() {
    if (this.commonTitle == "退回原因") {
      this.sendBackReason();
    }
  },
  methods: {
    //特殊工资单详情
    async getDetail() {
      const { data } =
        await this.$api.logisticsInformation.payrollManagement.salaryDetail({
          id: this.info.id,
        });
      this.printInfo = data;
      this.tableList = data.list;
      this.total = data.list.length;
      this.name = this.payrollOptions.find(
        (item) => item.value == this.printInfo.type
      ).name;
      this.getList();
    },
    getList() {
      this.tableData = this.tableList.slice(
        (this.pageNum - 1) * 4,
        this.pageNum * 4
      );
      console.log("this.tableData", this.tableData);
      this.size = `${this.pageNum}/${Math.ceil(this.tableList.length / 4)}`;
      this.pageSize = this.tableList.length;
    },
    //退回原因
    async sendBackReason() {
      const { data } =
        await this.$api.logisticsInformation.payrollManagement.sendBackReason({
          id: this.info.id,
        });
      this.reasonInfo = data || {};
    },
    filterData(value) {
      return moneyDelete(value);
    },
    print() {
      let printList = [];
      let items = ["staffCode", "accountingMonth", "count"];
      let obj = {
        staffCode: this.printInfo.staffCode,
        staffName: this.printInfo.staffName,
        factoryName: this.printInfo.factoryName,
        processName: this.printInfo.processName,
        position: this.printInfo.position,
        no: this.printInfo.no,
        type: this.name,
        tableName: this.printInfo.tableName,
        // tableName: "",
        auditName: "",
        handleTime: this.printInfo.handleTime || "",
        remark: this.printInfo.remark || "",
        pageSize: this.pageSize,
        printCount: this.printInfo.printCount,
        dataList: this.pageData(this.tableList),
      };
      let dataList =
        obj.dataList &&
        obj.dataList.map((it) => {
          let list = it.list.map((item) => {
            let params = {
              accountingMonth: item.accountingMonth,
            };
            Object.keys(item).forEach((key) => {
              if (!items.includes(key)) {
                if (
                  item[key].settlementAmount == "0.00" &&
                  key != "attendances"
                ) {
                  params[key] = "-";
                } else {
                  params[key] = moneyFormat(item[key].settlementAmount);
                }
              }
              // if (!items.includes(key)) {
              //   if (!["自离工资", "延发工资"].includes(this.name)) {
              //     if (
              //       item[key].settlementAmount == "0.00" &&
              //       key != "attendances"
              //     ) {
              //       params[key] = "-";
              //     } else {
              //       params[key] = moneyFormat(item[key].settlementAmount);
              //     }
              //   } else {
              //     if (["actualSalary"].includes(key)) {
              //       if (item[key].settlementAmount == "0.00") {
              //         params[key] = "-";
              //       } else {
              //         params[key] = moneyFormat(item[key].settlementAmount);
              //       }
              //     } else {
              //       params[key] = "-";
              //     }
              //   }
              // }
            });
            return params;
          });
          return {
            ...it,
            list,
          };
        });
      printList.push({ ...obj, dataList });
      //     let params = {
      //   LabelName: "label2",
      //   MessageType: "print",
      //   PrintData: printList,
      // };
      //     this.$api.information.employee.employeeDetails(params).then((res)=>{})
      soket(printList);
    },
    pageData(tableList) {
      let arr = [];
      for (let index = 0; index < Math.ceil(tableList.length / 4); index++) {
        let obj = {};
        let data = tableList.slice(index * 4, (index + 1) * 4);
        obj = {
          list: data,
          smallNum: moneyFormat(getSum(data)),
          bigNum: dealBigMoney(moneyFormat(getSum(data))),
        };
        arr.push(obj);
      }
      return arr;
    },
    //打印
    handlePrint() {
      if (Number(this.printInfo.printCount) + 1 < 2) {
        this.$api.logisticsInformation.payrollManagement.increment({
          ids: [this.printInfo.id],
        });
        this.print();
        return;
      }
      this.$confirm("打印次数超过2次,是否打印", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$api.logisticsInformation.payrollManagement.increment({
            ids: [this.printInfo.id],
          });
          this.print();
        })
        .catch(() => {});
    },
    onNumChange(val) {
      this.pageNum = val;
      this.getList();
    },
    handleCancel() {
      this.$emit("cancel", {
        type: "cancel",
        isVisible: false,
      });
    },
    handleConfirm() {
      let params =
        this.info.isAll == "Y"
          ? { isAll: "Y" }
          : {
              isAll: "N",
              ids: this.info.idList,
            };
      switch (this.commonTitle) {
        case "批量提交":
          this.isLoading = true;
          this.$api.logisticsInformation.payrollManagement
            .batchSubmit({ handleStatus: "0", ...params })
            .then(() => {
              this.$notify.success({
                title: "成功",
                message: "批量提交成功",
              });
              this.$emit("cancel", {
                type: "confirm",
                isVisible: false,
              });
            })
            .finally(() => {
              this.isLoading = false;
            });
          break;
        case "批量审核":
          this.isLoading = true;
          this.$api.logisticsInformation.payrollManagement
            .batchAudit({ handleStatus: "1", ...params })
            .then(({ data }) => {
              let printList = [];
              let items = [
                "staffCode",
                "accountingMonth",
                "count",
                "actualSalary",
              ];
              data &&
                data.forEach((item) => {
                  this.name = this.payrollOptions.find(
                    (m) => m.value == item.type
                  ).name;
                  let obj = {
                    staffCode: item.staffCode,
                    staffName: item.staffName,
                    factoryName: item.factoryName,
                    processName: item.processName,
                    no: item.no,
                    // type: this.payrollOptions.find(m => m.value == item.type).name,
                    type: this.name,
                    // tableName: "",
                    tableName: this.printInfo.tableName,
                    auditName: "",
                    handleTime: item.handleTime || "",
                    remark: item.remark || "",
                    pageSize: item.list.length || 0,
                    printCount: item.printCount,
                    dataList: this.pageData(item.list),
                  };
                  let dataList =
                    obj.dataList &&
                    obj.dataList.map((it) => {
                      let list =
                        it.list &&
                        it.list.map((item) => {
                          let params = {
                            accountingMonth: item.accountingMonth,
                          };
                          // Object.keys(item).forEach(key => {
                          //   if (!items.includes(key)) {
                          //     if (item[key]) {
                          //       if (key != 'attendances' && item[key].settlementAmount == '0.00') {
                          //         params[key] = '-'
                          //       } else {
                          //         params[key] = moneyFormat(item[key].settlementAmount)
                          //       }
                          //     }
                          //   }
                          // })
                          Object.keys(item).forEach((key) => {
                            if (!items.includes(key)) {
                              if (
                                !["自离工资", "延发工资"].includes(this.name)
                              ) {
                                if (
                                  item[key].settlementAmount == "0.00" &&
                                  key != "attendances"
                                ) {
                                  params[key] = "-";
                                } else {
                                  params[key] = moneyFormat(
                                    item[key].settlementAmount
                                  );
                                }
                              } else {
                                if (["actualSalary"].includes(key)) {
                                  if (item[key].settlementAmount == "0.00") {
                                    params[key] = "-";
                                  } else {
                                    params[key] = moneyFormat(
                                      item[key].settlementAmount
                                    );
                                  }
                                } else {
                                  params[key] = "-";
                                }
                              }
                            }
                          });
                          return params;
                        });
                      return {
                        ...it,
                        list,
                      };
                    });
                  printList.push({ ...obj, dataList });
                });
              soket(printList);
              this.$notify.success({
                title: "成功",
                message: "批量审核成功",
              });
              this.$emit("cancel", {
                type: "confirm",
                isVisible: false,
              });
            })
            .finally(() => {
              this.isLoading = false;
            });
          break;
        case "作废":
          this.$refs.cancelForm.validate((valid) => {
            if (!valid) return;
            this.$api.logisticsInformation.payrollManagement
              .revoke({ id: this.info.id })
              .then(() => {
                this.$notify.success({
                  title: "成功",
                  message: "作废成功",
                });
                this.$emit("cancel", {
                  type: "confirm",
                  isVisible: false,
                });
              })
              .finally(() => {
                this.isLoading = false;
              });
          });
          break;
        case "提交":
          this.isLoading = true;
          this.$api.logisticsInformation.payrollManagement
            .submit({ id: this.info.id })
            .then(() => {
              this.$notify.success({
                title: "成功",
                message: "提交成功",
              });
              this.$emit("cancel", {
                type: "confirm",
                isVisible: false,
              });
            })
            .finally(() => {
              this.isLoading = false;
            });
          break;
        case "通过":
          this.isLoading = true;
          this.$api.logisticsInformation.payrollManagement
            .audit({ id: this.info.id })
            .then(({ data }) => {
              this.printInfo = data;
              this.tableList = data.list;
              this.total = data.list.length;
              this.name = this.payrollOptions.find(
                (item) => item.value == this.printInfo.type
              ).name;
              this.pageSize = this.tableList.length;
              this.print();
              this.$notify.success({
                title: "成功",
                message: "通过成功",
              });
              this.$emit("cancel", {
                type: "confirm",
                isVisible: false,
              });
            })
            .finally(() => {
              this.isLoading = false;
            });
          break;
        case "退回":
          this.$refs.reasonForm.validate((valid) => {
            if (!valid) return;
            this.isLoading = true;
            this.$api.logisticsInformation.payrollManagement
              .sendBack({ id: this.info.id, reason: this.reasonForm.reason })
              .then(() => {
                this.$notify.success({
                  title: "成功",
                  message: "退回成功",
                });
                this.$emit("cancel", {
                  type: "confirm",
                  isVisible: false,
                });
              })
              .finally(() => {
                this.isLoading = false;
              });
          });
          break;
        case "退回原因":
          this.$emit("cancel", {
            type: "cancel",
            isVisible: false,
          });
          break;

        default:
          break;
      }
    },
  },
};
</script>

<style lang="stylus" scoped>
section {
  border: 1px solid #000;
  padding: 10px 8px;
  margin-bottom: 10px;
  }
  .btn {
    text-align: right;
    margin-bottom: 10px;
  }


.header {
  margin-bottom: 10px;

  &_top {
    position: relative;
    margin-bottom: 15px;

    >div {
      position: absolute;
    }

    h3 {
      text-align: center;
    }
  }

  &_bottom {
    display: flex;
    justify-content: space-between;

    .item {
      // flex: 1;

      // &:nth-of-type(2),  {
      //   flex: 0.5;
      // }
      //   &:nth-of-type(3),  {
      //   flex: 0.6;
      // }

      // & :nth-of-type(7) {
      //   flex: 0.2;
      // }
    }
  }
}

.table-footer {
  margin-top: 10px;

  #table_footer {
    display: flex;
    justify-content: space-between;
    margin: 20px 0px;
  }
}

.footer {
  width: 100%;
  padding: 20px 0;
  display: flex;
  justify-content: space-between;
}

.reason {
  .item {
    padding: 5px 0;
  }
}

.cancelForm {
  >>> .el-form-item__label {
    &::before {
      display: none;
    }
  }
}

>>>.el-textarea__inner {
  padding-right: 50px;
}

.el-textarea {
  >>>.el-input__count {
    right: 20px !important;
  }
}

>>>.el-table th.el-table__cell > .cell {
  white-space: pre;
}
</style>
