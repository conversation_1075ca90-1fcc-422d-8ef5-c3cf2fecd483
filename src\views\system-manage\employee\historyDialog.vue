<template>
  <qDialog :visible="isVisible" title="历史数据" :innerHeight="150" width="600px" @cancel="handleCancel"
    @confirm="handleConfirm" :before-close="handleCancel">
    <el-table stripe border v-loading="loading" ref="tableRef" height="150" highlight-current-row :data="tableData">
      <el-table-column width="40" type="index" align="left" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="staffCode" label="厂牌编号" align="left" width="120" show-overflow-tooltip>
      </el-table-column>
      <el-table-column label="更新时间" align="left" width="150" show-overflow-tooltip>
        <template slot-scope="{row}">
          {{ row.updateTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column prop="subProcess" label="子工序" align="left" width="80" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="productionCategory" label="员工类别" align="left" width="80" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="category" label="员工类别" align="left" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="jobStatus" label="员工状态" align="left" show-overflow-tooltip>
      </el-table-column>
      <el-table-column label="操作" align="left" width="70">
        <template slot-scope="{row}">
          <el-button v-show="row.jobStatus == '离职' && row.freezeStatus == 'Y'" size="small" type="text"
            @click="handleThaw(row)">
            解冻
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </qDialog>
</template>

<script>
export default {
  name: "historyDialog",
  props: {
    isVisible: {
      type: Boolean,
      required: true
    },
    editForm: Object
  },
  data() {
    return {
      tableData: [],
      loading: false
    };
  },
  created() {
    this.getDetails();
  },
  methods: {
    //获取员工详情
    getDetails() {
      this.loading = true;
      this.$api.information.employee.historyStaff({ staffCode: this.editForm.staffCode }).then(({ data }) => {
        this.tableData = data || [];
      }).finally(() => {
        this.loading = false;
      });
    },
    //解冻
    handleThaw({ staffId }) {
      this.$api.information.employee.unfreezeStaff({ id: staffId }).then(() => {
        this.$notify.success({
          title: "成功",
          message: "解冻成功",
        });
        this.getDetails();
      });
    },
    handleCancel() {
      this.$emit('historyCancel', 'cancel');
    },
    handleConfirm() {
      this.$emit('historyCancel', 'cancel');
    }
  }
};
</script>

<style lang="stylus" scoped></style>