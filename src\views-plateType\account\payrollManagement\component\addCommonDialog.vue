<template>
  <qDialog
    :visible="editVisible"
    :innerScroll="true"
    :innerHeight="height"
    :title="editTitle"
    :showFooter="showFooter"
    :width="width"
    :modal-append-to-body="false"
    append-to-body
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel"
  >
    <div v-if="editTitle == '选择月份'">
      <el-form
        :model="monthForm"
        ref="monthForm"
        :rules="monthRules"
        label-width="88px"
        size="small"
      >
        <el-form-item label="选择月份:">
          <el-select
            v-model="monthForm.accountingMonth"
            filterable
            clearable
            multiple
            collapse-tags
            placeholder="请选择月份"
          >
            <el-option
              v-for="month in monthList"
              :key="month"
              :label="month"
              :value="month"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <div v-else>
      <div class="edit_header">
        <el-form>
          <el-row>
            <el-col :span="row">
              <el-form-item label="员工姓名:">
                {{ basicInfo.staffName }}
              </el-form-item>
            </el-col>
            <el-col :span="row">
              <el-form-item label="厂牌编号:">
                {{ basicInfo.staffCode }}
              </el-form-item>
            </el-col>
            <el-col :span="row" v-if="editTitle != '编辑'"> </el-col>
          </el-row>
          <el-row>
            <el-col :span="row">
              <el-form-item label="工厂名称:">
                {{ basicInfo.factoryName }}
              </el-form-item>
            </el-col>
            <el-col :span="row">
              <el-form-item label="结算月份:">
                {{ basicInfo.accountingMonth }}
              </el-form-item>
            </el-col>
            <el-col v-if="editTitle != '编辑'" :span="row">
              <el-form-item label="制表人员:">
                {{ basicInfo.tableName }}
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <el-table border stripe :data="tableData" style="width: 100%">
        <el-table-column type="index" width="50"> </el-table-column>
        <el-table-column prop="name" label="数据名称" width="100"> </el-table-column>
        <el-table-column prop="value" label="系统数值" width="100">
          <template slot-scope="{ row }">
            {{ filterValue(row) }}
          </template>
        </el-table-column>
        <el-table-column label="结算数值" width="130">
          <template slot-scope="scope">
            <el-input
              class="input_box"
              v-if="
                editTitle == '编辑' &&
                scope.row.name != '合计扣款' &&
                scope.row.name != '实发工资'
              "
              oninput="if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+4)}"
              ref="inputRef"
              clearable
              v-model.trim="scope.row.settlementAmount"
              @blur="onBlur(scope.$index, scope.row)"
              @clear="clearData(scope.$index, scope.row)"
            >
            </el-input>
            <span
              v-else
              :style="{
                color:
                  editTitle != '编辑'
                    ? filterData(scope.row.settlementAmount) ==
                      filterData(scope.row.sysAmount)
                      ? '#0BB78E'
                      : 'red'
                    : '',
              }"
              >{{ scope.row.settlementAmount }}</span
            >
          </template>
        </el-table-column>
        <el-table-column v-if="editTitle != '编辑'" label="修改人">
          <template slot-scope="{ row }">
            {{ row.updateName }}
          </template>
        </el-table-column>
        <el-table-column v-if="editTitle != '编辑'" label="修改时间">
          <template slot-scope="{ row }">
            {{ row.updateTime }}
          </template>
        </el-table-column>
        <el-table-column label="备注说明" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <el-input
              v-if="editTitle == '编辑'"
              class="input_box"
              v-model="row.remark"
              type="textarea"
              resize="none"
              rows="2"
              show-word-limit
              maxlength="300"
              clearable
              placeholder="请输入备注说明"
            >
            </el-input>
            <span v-else> {{ row.remark }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </qDialog>
</template>

<script>
import moment from "moment";
import { moneyFormat, moneyDelete } from "@/utils";
import { numAdd } from "../common";
import NP from "number-precision";
export default {
  props: {
    editVisible: {
      type: Boolean,
      required: true,
    },
    editTitle: {
      type: String,
      required: true,
    },
    addInfo: [Object],
  },
  data() {
    return {
      height: 0,
      width: "",
      row: 0,
      showFooter: true,
      monthForm: {
        accountingMonth: "",
      },
      monthRules: {},
      tableData: [
        {
          name: "出勤天数",
          value: "attendances",
          attendances: "",
          settlementAmount: "",
          remark: "",
        },
        {
          name: "工资总额",
          value: "salary",
          salary: "",
          settlementAmount: "",
          remark: "",
        },
        {
          name: "其他扣款",
          value: "otherDeduct",
          otherDeduct: "",
          settlementAmount: "",
          remark: "",
        },
        {
          name: "厂服扣款",
          value: "uniformDeduct",
          uniformDeduct: "",
          settlementAmount: "",
          remark: "",
        },
        {
          name: "生活费",
          value: "living",
          living: "",
          settlementAmount: "",
          remark: "",
        },
        {
          name: "保险",
          value: "insurance",
          insurance: "",
          settlementAmount: "",
          remark: "",
        },
        {
          name: "工会",
          value: "labour",
          labour: "",
          settlementAmount: "",
          remark: "",
        },
        {
          name: "借支",
          value: "loan",
          loan: "",
          settlementAmount: "",
          remark: "",
        },
        {
          name: "自离扣款",
          value: "leaveDeduct",
          leaveDeduct: "",
          settlementAmount: "",
          remark: "",
        },
        {
          name: "合计扣款",
          value: "totalDeduct",
          totalDeduct: "",
          settlementAmount: "",
          remark: "",
        },
        {
          name: "实发工资",
          value: "actualSalary",
          actualSalary: "",
          settlementAmount: "",
          remark: "",
        },
      ],
      basicInfo: {},
      monthList: [],
    };
  },
  watch: {
    editTitle: {
      handler(value) {
        switch (value) {
          case "选择月份":
            this.width = "400px";
            this.height = 100;
            this.showFooter = true;
            break;
          case "编辑":
            this.width = "700px";
            this.row = 12;
            this.height = 600;
            this.showFooter = true;
            break;

          default:
            this.width = "900px";
            this.row = 8;
            this.height = 400;
            this.showFooter = false;
            break;
        }
      },
      immediate: true,
    },
  },
  created() {
    this.monthList = this.getMonthList();
    if (this.editTitle == "选择月份") {
      this.monthForm.accountingMonth = this.addInfo.obj.isClearMonth
        ? []
        : this.addInfo.months;
    }
    this.$bus.$off("plateTypeList");
    this.$bus.$on("plateTypeList", (data) => {
      const { editData, obj } = data;
      this.tableData = this.tableData.reduce((pre, cur) => {
        let item;
        for (const key in editData[0]) {
          item = {
            ...cur,
            [cur.value]: editData[0][cur.value].sysAmount,
            sysAmount: editData[0][cur.value].sysAmount,
            settlementAmount:
              cur.name == "出勤天数"
                ? editData[0][cur.value].settlementAmount
                : moneyFormat(editData[0][cur.value].settlementAmount),
            oldSettlementAmount: moneyFormat(editData[0][cur.value].settlementAmount),
            updateName: editData[0][cur.value].updateName,
            updateTime: editData[0][cur.value].updateTime,
            remark: editData[0][cur.value].remark || "",
          };
        }
        pre.push(item);
        return pre;
      }, []);
      data &&
        (this.basicInfo = {
          factoryName: obj.factoryName,
          staffCode: obj.staffCode,
          staffName: obj.staffName,
          accountingMonth: obj.accountingMonth,
          tableName: obj.tableName,
          count: obj.count,
        });
    });
  },
  methods: {
    getMonthList() {
      // 获取当前时间
      const now = moment();
      // 获取两年前的时间
      const twoYearsAgo = now.clone().subtract(2, "years");
      // 构建一个数组，用于存储所有的月份
      const months = [];
      // 循环遍历每个月份
      for (let m = twoYearsAgo.clone(); m.isSameOrBefore(now); m.add(1, "month")) {
        // 将当前月份添加到数组中
        months.push(m.format("YYYY-MM"));
      }
      return months.reverse();
    },
    filterData(value) {
      return moneyDelete(value);
    },
    filterValue({ name, sysAmount }) {
      return name == "出勤天数" ? sysAmount : moneyFormat(sysAmount);
    },
    init(index) {
      this.tableData = this.tableData.map((m, n) => {
        if (n == index) {
          m = {
            ...m,
            settlementAmount: m.oldSettlementAmount,
          };
        }
        return m;
      });
    },
    clearData(index, row) {
      let tableData = this.tableData.map((m, n) => {
        if (n == index) {
          m = {
            ...m,
            settlementAmount: "0.00",
          };
        }
        return m;
      });
      let list = ["出勤天数", "工资总额", "合计扣款", "实发工资"];
      const salary = tableData.find((item) => item.name == "工资总额").settlementAmount;
      const total = tableData
        .filter((item) => !list.includes(item.name))
        .reduce(
          (pre, cur) => {
            pre.totalDeduct = NP.plus(pre.totalDeduct,Number(moneyDelete(cur.settlementAmount)))
            pre.actualSalary = NP.minus(Number(moneyDelete(salary)),pre.totalDeduct)
            return pre;
          },
          { totalDeduct: 0, actualSalary: 0 }
        );
      this.tableData = tableData.map((item) => {
        Object.keys(total).forEach((key) => {
          if (item.value == key) {
            item.settlementAmount = moneyFormat(total[key]);
          }
        });
        return item;
      });
    },
    onBlur(index, row) {
      if (!row.settlementAmount) return;
      if (row.name == "出勤天数") {
        if (!/^\d{1,2}(\.\d{1,3})?$/.test(moneyDelete(row.settlementAmount))) {
          this.$message({
            message: "小数点前面仅支持2位数,小数点后面仅支持3位数",
            type: "warning",
          });
          this.init(index);
        }
        return;
      }
      if (!/^-?\d{1,5}(\.\d{1,2})?$/.test(moneyDelete(row.settlementAmount))) {
        this.$message({
          message: "小数点前面仅支持5位数,小数点后面仅支持2位数",
          type: "warning",
        });
        this.init(index);
        return;
      }
      this.tableData = this.tableData.map((m, n) => {
        if (n == index) {
          m = {
            ...m,
            settlementAmount: moneyFormat(moneyDelete(row.settlementAmount)),
          };
        }
        return m;
      });
      let list = ["出勤天数", "工资总额", "合计扣款", "实发工资"];
      const salary = this.tableData.find((item) => item.name == "工资总额")
        .settlementAmount;
      const total = this.tableData
        .filter((item) => !list.includes(item.name))
        .reduce(
          (pre, cur) => {
            pre.totalDeduct = NP.plus(pre.totalDeduct,Number(moneyDelete(cur.settlementAmount)))
            pre.actualSalary = NP.minus(Number(moneyDelete(salary)),pre.totalDeduct)
            return pre;
          },
          { totalDeduct: 0, actualSalary: 0 }
        );
      this.tableData = this.tableData.map((item) => {
        Object.keys(total).forEach((key) => {
          if (item.value == key) {
            item.settlementAmount = moneyFormat(total[key]);
          }
        });
        return item;
      });
    },
    handleCancel() {
      this.$emit("cancel", {
        type: "cancel",
        isVisible: false,
      });
    },
    handleConfirm() {
      switch (this.editTitle) {
        case "选择月份":
          if(!this.monthForm.accountingMonth.length){
            return this.$message.error("请选择月份")
          }
          let params = {
            months: this.monthForm.accountingMonth,
            factoryId: this.addInfo.obj.factoryId,
            staffCode: this.addInfo.obj.staffCode,
            type:this.addInfo.obj.type
          };
          this.$api.plateTypeInformation.payrollManagement.monthSalary(params).then(({ data }) => {
            let months = params.months;
            let list = [];
            if (data.filter((item) => item != null).length > 0) {
              data
                .filter((item) => item != null)
                .map((item) => {
                  let findIndex = months.findIndex(
                    (month) => month == item.accountingMonth
                  );
                  months.splice(findIndex, 1);
                });
            }
            months.forEach((month) => {
              list.push({
                accountingMonth: month,
                actualSalary: "0.00",
                attendances: "0.00",
                factoryId: "0.00",
                insurance: "0.00",
                labour: "0.00",
                living: "0.00",
                loan: "0.00",
                otherDeduct: "0.00",
                salary: "0.00",
                staffCode: "0.00",
                totalDeduct: "0.00",
                uniformDeduct: "0.00",
                leaveDeduct:"0.00"
              });
            });
            this.$bus.$emit("plateTypeMonth", {
              editData: [...data, ...list].filter((item) => item != null),
              obj: { ...this.addInfo },
            });
          });
          break;
        case "编辑":
          let mapObj = {
            attendances: {
              itemType: "1",
              sortNo: "1",
            },
            salary: {
              itemType: "2",
              sortNo: "2",
            },
            otherDeduct: {
              itemType: "3",
              sortNo: "3",
            },
            uniformDeduct: {
              itemType: "4",
              sortNo: "4",
            },
            living: {
              itemType: "5",
              sortNo: "5",
            },
            insurance: {
              itemType: "6",
              sortNo: "6",
            },
            labour: {
              itemType: "7",
              sortNo: "7",
            },
            loan: {
              itemType: "8",
              sortNo: "8",
            },
            totalDeduct: {
              itemType: "9",
              sortNo: "9",
            },
            actualSalary: {
              itemType: "10",
              sortNo: "10",
            },
            leaveDeduct:{
              itemType: "11",
              sortNo: "11",
            },
          };
          let details = [];
          this.tableData.forEach((item) => {
            for (const key in mapObj) {
              if (item.value === key) {
                mapObj[key] = {
                  ...mapObj[key],
                  sysAmount: moneyDelete(item[key]),
                  settlementAmount: item.settlementAmount
                    ? moneyDelete(item.settlementAmount)
                    : "0.00",
                  remark: item.remark,
                };
              }
            }
          });
          details.push({
            accountingMonth: this.basicInfo.accountingMonth,
            count: this.basicInfo.count,
            ...mapObj,
          });
          this.$bus.$emit("plateTypeTable", details);
          break;

        default:
          break;
      }
      this.$emit("cancel", {
        type: "confirm",
        isVisible: false,
      });
    },
  },
};
</script>

<style lang="stylus" scoped>
>>>.el-select {
  width: 100%;
}

.edit_header {
  >>>.el-form-item {
    margin-bottom: 5px;
  }
}

>>>.input_box {
  margin: 3px 0;
}

>>>.el-textarea__inner {
  padding-right: 45px;
}

.el-textarea {
  >>>.el-input__count {
    right: 20px !important;
  }
}
</style>
