<template>
  <qDialog
    :visible="visible"
    :innerScroll="false"
    :innerHeight="500"
    :title="title"
    width="500px"
    :isLoading="isLoading"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel"
  >
    <div class="illustrate">说明:员工须有本厂的考勤,否则无法新增</div>
    <el-form
      :model="addForm"
      :rules="rules"
      ref="addForm"
      size="small"
      label-width="160px"
    >
      <el-row :gutter="10">
        <el-col :span="24">
          <el-form-item
            v-if="title == '新增'"
            label="厂牌编号:"
            prop="staffCode"
          >
            <el-input
              class="staffCode"
              v-model.trim="addForm.staffCode"
              clearable
              placeholder="请输入厂牌编号"
            >
              <template slot="append">
                <el-button type="primary" @click="searchEmployee">
                  查询
                </el-button>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item v-else label="厂牌编号:">{{
            addForm.staffCode
          }}</el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item class="processCode" label="员工姓名:">{{
            addForm.staffName
          }}</el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item class="processCode" label="核算月份:"
            >{{ info.accountingMonth }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="核算班组(工序)" prop="groupId">
            <el-select
              clearable
              v-model.trim="addForm.groupId"
              placeholder="请选择核算班组（工序）"
              style="width: 100%"
              filterable
              :disabled="editForm.actionType == 'auto'"
            >
              <el-option
                v-for="item in processesOption"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="熟手补贴金额:" prop="subsidyAmount">
            <el-input
              clearable
              placeholder="请输入熟手补贴金额"
              v-model="addForm.subsidyAmount"
              type="number"
              class="no-spin-buttons"
            >
              <template slot="append">元</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item class="assignedFactory" label="备注:" prop="remarks">
            <el-input
              type="textarea"
              v-model="addForm.remarks"
              resize="none"
              rows="3"
              maxlength="50"
              show-word-limit
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </qDialog>
</template>

<script>
import { numAdd } from "@/utils";
import moment from "moment";
export default {
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    title: {
      type: String,
      required: true,
    },
    editForm: Object,
    info: Object,
  },
  data() {
    return {
      addForm: {
        staffName: "",
        staffCode: "",
        groupId: "",
        subsidyAmount: "",
      },
      processesOption: [],
      rules: {
        staffCode: [
          { required: true, message: "厂牌编号不能为空", trigger: "blur" },
        ],
        groupId: [
          { required: true, message: "核算班组不能为空", trigger: "change" },
        ],

        subsidyAmount: [
          {
            required: true,
            pattern: /^(0|[0-9]{1,5})((\.\d{0,2})*)$/,
            message: "小数点前面仅支持5位数,小数点后面仅支持2位数",
            trigger: "blur",
          },
        ],
      },
      showType: false,
      idCardRsa: "",
      isLoading: false,
    };
  },
  async created() {
    if (this.title == "编辑") {
      const {
        staffName,
        staffCode,
        groupId,
        subsidyAmount,
        id,
        accountingMonth,
        remarks,
      } = this.editForm;
      this.addForm = {
        staffName,
        staffCode,
        groupId,
        subsidyAmount,
        id,
        accountingMonth,
        remarks,
      };
      this.getGroups();
    }
  },

  methods: {
    //查询员工信息
    searchEmployee() {
      if (!this.addForm.staffCode) {
        this.$message.warning("请输入厂牌编号");
        return;
      }
      this.$api.information.employee
        .employeeDetails({ staffCode: this.addForm.staffCode })
        .then(({ data }) => {
          this.addForm.staffName = data.staffName || "";
        });
      this.getGroups();
    },
    //身份证解码
    toggle() {
      if (this.showType) {
        this.addForm.idCard = this.addForm.idCardOrigin;
        this.showType = false;
      } else {
        if (this.addForm.idCardDecoded) {
          this.addForm.idCard = this.addForm.idCardDecoded;
          this.showType = true;
          return;
        }
        this.$api.information.employee.decrypt(this.idCardRsa).then((res) => {
          this.addForm.idCard = res.data;
          this.addForm.idCardDecoded = res.data;
          this.showType = true;
        });
      }
    },
    handleCancel() {
      this.$emit("cancel", "cancel");
    },
    handleConfirm() {
      this.$refs.addForm.validate((valid) => {
        if (!valid) return;
        if (!this.addForm.staffName) {
          this.$message.warning("员工姓名为空,请先查询员工姓名");
          return;
        }
        this.isLoading = true;
        const { factoryId, accountingMonth } = JSON.parse(
          this.$Base64.decode(this.$route.query.data)
        );
        let params = {
          ...this.addForm,
          factoryId,
          accountingMonth,
        };
        let fullApi = this.title == "新增" ? "ldhandadd" : "ldhandedit";
        let msg = this.title == "新增" ? "新增成功" : "编辑成功";
        this.$api.softwareDataUpload.ldhandsubsidy[fullApi](params)
          .then(() => {
            this.$notify({
              title: "成功",
              message: msg,
              type: "success",
            });
            this.$emit("cancel", "confirm");
          })
          .finally(() => {
            this.isLoading = false;
          });
      });
    },
    //  获取核算班组下拉列表
    getGroups() {
      let params = {
        accountingMonth: this.info.accountingMonth,
        factoryId: this.info.factoryId,
        // staffCode:this.addForm.staffCode,
      };
      this.$api.softwareSystemManage.getBasicPermission
        .availableGroup(params)
        .then((res) => {
          console.log("res", res);

          // this.handleArr(res.data);
          this.processesOption = res.data;
        });
    },
  },
};
</script>

<style lang="stylus" scoped>
.staffCode {
  >>>.el-input-group__append {
    background: #24c69a;
    color: #fff;

    .el-button {
      padding: 5px 20px;
    }
  }
}

.el-row {
  &::before, &::after {
    display: none;
  }

  display: flex;
  justify-content: space-between;
}

.el-form-item {
  display: flex;

  >>>.el-form-item__label {
    text-align: right;

  }

  >>>.el-form-item__content {
    width: 100%;
    margin-left: 0 !important;
  }
}

.assignedFactory {
  >>>.el-form-item__label {
    &::before {
      content: '*';
      color: #F23D20;
      margin-right: 4px;
      visibility: hidden;
    }
  }
}

>>>.el-date-editor {
  width: 100%;
}
.illustrate{
  color:#FF8900;
  padding:10px;
  margin-left:15px
}
::v-deep .no-spin-buttons input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}

::v-deep .no-spin-buttons input::-webkit-outer-spin-button {
  -webkit-appearance: none !important;
}

::v-deep .no-spin-buttons input[type="number"] {
  -moz-appearance: textfield !important;
}
</style>
