<template>
  <content-panel>
    <template v-slot:search>
      <search-box class="search-box">
        <el-form size="mini" :inline="true" :model="searchForm" ref="searchForm">
          <el-form-item label="员工姓名:" prop="staffName">
            <el-input clearable v-model.trim="searchForm.staffName" size="mini" @keyup.enter.native="onSearch">
              <template slot="append">
                <search-batch @seachFilter="onSearch('staffNames', $event)"
                  @focusEvent="focusEvent('staffNames', $event)" ref="childrenStaffNames" titleName="员工姓名" />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="厂牌编号:" prop="staffCode">
            <el-input v-model.trim="searchForm.staffCode" size="mini" clearable @keyup.enter.native="onSearch">
              <template slot="append">
                <search-batch @seachFilter="onSearch('staffCodes', $event)"
                  @focusEvent="focusEvent('staffCodes', $event)" ref="childrenStaffCodes" titleName="厂牌编号" />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="核算班组:" prop="processId">
            <el-select v-model="searchForm.processId" filterable clearable placeholder="请选择核算班组 " @change="onSearch">
              <el-option v-for="item in procedureOptions" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="多班组:" prop="isDup">
            <el-radio-group v-model="searchForm.isDup" @input="onSearch">
              <el-radio label="2">全部 </el-radio>
              <el-radio label="1">是 </el-radio>
              <el-radio label="0">否 </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button size="small" type="primary" @click="onSearch">
            查询
          </el-button>
          <el-button size="small" type="warning" @click="resetSearchForm">
            重置
          </el-button>
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <template v-slot:header-left>
        <ul>
          <li>
            <span>核算工厂:</span><span>{{ attendanceStatisticInfo.factoryName }}</span>
          </li>
          <li>
            <span>核算月份:</span><span>{{ attendanceStatisticInfo.accountingMonth }}</span>
          </li>
          <li>
            <span>人数:</span><span>{{ attendanceStatisticInfo1.sysTotal }}</span>
          </li>
          <li>
            <span>本厂出勤天数:</span><span>{{ attendanceStatisticInfo1.totalWorkDay }}</span>
          </li>
          <li>
            <span>本厂加班小时:</span><span>{{ attendanceStatisticInfo1.totalOvertime }}</span>
          </li>
          <li></li>
          <li></li>
        </ul>
      </template>
      <template v-slot:header-right>
        <div ref="btnRight" style="display: flex; justify-content: space-between">
          <el-button v-show="permission" v-permission="'was-customized$workBench$customized$workOverview$attendanceAdd'
            " size="small" type="primary" @click="handleAdd">
            新增
          </el-button>
          <el-button size="small" type="primary" @click="handleImport" v-show="permission">
            导入
          </el-button>
          <el-button size="small" type="primary" @click="handleExport"> 导出 </el-button>
        </div>
      </template>
      <el-table stripe border v-loading="loading" ref="tableRef" highlight-current-row :height="maxTableHeight"
        :data="tableData">
        <el-table-column prop="staffName" label="员工姓名" width="80" align="left" fixed show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="staffCode" label="厂牌编号" width="120" align="left" fixed show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="accountingMonth" label="核算月份" width="80" align="left">
        </el-table-column>
        <el-table-column prop="accountingProcessName" label="核算班组" width="100" align="left">
        </el-table-column>
        <el-table-column prop="subProcess" label="子工序" width="120" align="left" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="productionCategory" label="员工类别" width="120" align="left" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="totalWorkDay" label="本厂出勤天数" width="120" align="left" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="totalOvertime" label="本厂加班小时" width="120" align="left" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="workdayWork" label="工作日出勤（天）" width="130" align="left">
        </el-table-column>
        <el-table-column prop="workdayOvertime" label="工作日延时加班（小时）" width="175" align="left">
        </el-table-column>
        <el-table-column prop="weekWork" label="周末出勤（天）" width="120" align="left">
        </el-table-column>
        <el-table-column prop="weekOvertime" label="周末延时加班（小时）" width="160" align="left">
        </el-table-column>
        <el-table-column prop="holidayWork" label="节假日出勤（天）" width="130" align="left">
        </el-table-column>
        <el-table-column prop="holidayOvertime" label="节假日延时加班（小时）" width="175" align="left">
        </el-table-column>
        <el-table-column prop="comments" label="备注说明" align="left" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="updateTime" label="修改时间" align="left" width="105" show-overflow-tooltip>
        </el-table-column>
        <el-table-column align="left" width="150" fixed="right">
          <template slot-scope="scope">
            <el-button v-if="editPermission" v-permission="'was-customized$workBench$customized$workOverview$attendanceAdd'
              " size="small" type="text" @click="handlerEdit(scope.$index, scope.row)">
              编辑
            </el-button>
            <el-button v-if="permission" v-permission="'was-customized$workBench$customized$workOverview$attendanceDelete'
              " size="small" type="text" @click="handleDelete(scope.$index, scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <div style="text-align: right">
          <el-pagination @size-change="onSizeChange" @current-change="onNumChange" :current-page="pageNum"
            :page-size="pageSize" :total="total" :page-sizes="[50, 100, 200]"
            layout="total, sizes, prev, pager, next, jumper">
          </el-pagination>
        </div>
      </template>
    </table-panel>
    <add-dialog v-if="visible" :visible="visible" :title="title" :editForm="editForm"
      @cancel="handleCancel"></add-dialog>
    <review-dialog v-if="isVisible" :isVisible="isVisible" :title="title" @review="review"></review-dialog>
    <Import v-if="ImportVisible" :visible="ImportVisible" @cancel="cancel" @confirm="confirm"
      :importInfo="importInfo" />
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import addDialog from "./addDialog";
import ReviewDialog from "./commonDialog.vue";
export default {
  name: "Attendance",
  mixins: [tableMixin, pagePathMixin],
  components: {
    addDialog,
    ReviewDialog,
  },
  data() {
    return {
      searchForm: {
        staffCode: "",
        staffName: "",
        content: "",
        processId: "",
        isDup: "2",
      },
      editForm: {},
      procedureOptions: [],
      attendanceStatisticInfo: {}, //员工考勤统计
      attendanceStatisticInfo1: {},
      factoryId: "",
      tableData: [],
      loading: false,
      resizeOffset: 55,
      pageSize: 50,
      pageNum: 1,
      total: 0,
      permission: "",
      editPermission: "",
      title: "",
      filterParam: {},
      params: {},
      visible: false,
      ImportVisible: false,
      isVisible: false,
      importInfo: {},
      idList: [],
      isAll: false,
    };
  },
  watch: {
    $route: {
      handler(value) {
        if (value.path.includes("attendance") && value.path.includes("customized")) {
          this.attendanceStatisticInfo = JSON.parse(this.$Base64.decode(value.query.data));
          this.factoryId = this.attendanceStatisticInfo.factoryId;
          this.$nextTick(() => {
            this.$api.dataUpload.employeeAttendance
              .getGroups({
                factoryId: this.factoryId,
                accountingMonth: this.attendanceStatisticInfo.accountingMonth,
              })
              .then(({ data }) => {
                this.procedureOptions = (data && data.filter((item) => item)) || [];
              });
            this.importInfo = {
              reportName: "staffAttendanceImport",
              paramMap: {
                columnValue: "员工考勤",
                factoryId: this.factoryId,
                accountingMonth: this.attendanceStatisticInfo.accountingMonth,
                id: this.attendanceStatisticInfo.id,
              },
            };
            this.getList();
            this.getStatistic();
            this.getPermission("import");
            this.getPermission("edit");
          });
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    //获取员工考勤列表
    getList() {
      this.loading = true;
      this.$api.dataUpload.employeeAttendance
        .getEmployeeAttendanceList({
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          filterData: {
            factoryId: this.factoryId,
            accountingMonth: this.attendanceStatisticInfo.accountingMonth,
            ...this.filterParam,
            ...this.params,
            isDup: this.searchForm.isDup == "2" ? "" : this.filterParam.isDup,
          },
        })
        .then((res) => {
          if (res.code == 200) {
            this.tableData = res.data.list || [];
            this.total = res.data.total;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //是否具有代办任务操作权限
    getPermission(val) {
      let planStatus = val == "edit" ? { planStatus: [0, 1] } : {};
      let params = {
        factoryId: this.factoryId,
        accountingMonth: this.attendanceStatisticInfo.accountingMonth,
        type: "1",
        ...planStatus,
      };
      this.$api.workbench.agencyPermission(params).then((res) => {
        if (val == "edit") {
          this.editPermission = res.data;
        } else {
          this.permission = res.data;
        }
      });
    },
    //获取统计信息
    getStatistic() {
      let params = {
        accountingMonth: this.attendanceStatisticInfo.accountingMonth,
        factoryId: this.factoryId,
        ...this.filterParam,
        ...this.params,
        isDup: this.searchForm.isDup == "2" ? "" : this.filterParam.isDup,
      };
      this.$api.dataUpload.employeeAttendance
        .employeeAttendanceStatistics(params)
        .then((res) => {
          if (res.code === 200) {
            this.attendanceStatisticInfo1 = res.data || {};
          }
        });
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.$refs.childrenStaffNames.resetFilter();
      this.$refs.childrenStaffCodes.resetFilter();
      this.filterParam = {};
      this.params = {};
      this.onSearch();
    },
    //搜索
    onSearch(name, data) {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.filterParam[name] = data;
      }
      this.pageNum = 1;
      this.getList();
      this.getStatistic();
    },
    focusEvent(name, data) {
      if (Array.isArray(data) && !data.length) {
        this.params[name] = [];
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.params[name] = data;
      }
    },
    handleCancel({ type }) {
      this.visible = false;
      if (type == "cancel") return;
      this.getList();
      this.getStatistic();
    },
    //导入
    handleImport() {
      this.title = "导入";
      this.ImportVisible = true;
    },
    //新增
    handleAdd() {
      this.title = "新增";
      this.visible = true;
      this.editForm = {};
    },
    //编辑
    handlerEdit(index, row) {
      this.title = "编辑";
      this.visible = true;
      this.editForm = { ...row, processId: row.accountingProcessName };
    },
    //考勤审核
    handleReview() {
      this.title = "考勤审核";
      this.isVisible = true;
    },
    //导出
    handleExport() {
      let params = {
        factoryId: this.factoryId,
        accountingMonth: this.attendanceStatisticInfo.accountingMonth,
        ...this.filterParam,
        ...this.params,
      };
      this.$api.common.doExport("exportStaffAttendance", params).then(() => {
        this.$message.success("导出操作成功，请前往导出记录查看详情");
      });
    },
    review(type) {
      this.isVisible = false;
      if (type == "cancel") return;
      this.getList();
      this.getStatistic();
    },
    // handleSelectionChange(val) {
    //   this.isAll = false
    //   this.idList = val.map(item => item.id)
    // },
    // handleSelectionAll() {
    //   this.isAll = true
    // },
    //删除
    handleDelete(index, row) {
      this.$confirm("此操作将永久删除该条数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$api.dataUpload.employeeAttendance
            .deleteEmployee({ id: row.id })
            .then((res) => {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getList();
              this.getStatistic();
            });
        })
        .catch(() => { });
    },
    onSizeChange(pageSize) {
      this.pageSize = pageSize;
      // 切换每页条数后从第一页查询
      this.pageNum = 1;
      this.getList();
    },
    onNumChange(pageNum) {
      this.pageNum = pageNum;
      this.getList();
    },
    cancel(value) {
      this.ImportVisible = value;
    },
    confirm(value) {
      this.ImportVisible = value;
    },
  },
};
</script>

<style lang="stylus" scoped>
>>>.el-tabs__nav-wrap::after {
  height: 0;
}

ul {
  list-style: none;
  display: flex;
  align-items: center;
  padding: 0;
  margin: 0
  li{
    margin-right: 10px;
  }
}
i {
  font-style: normal;
}
</style>
