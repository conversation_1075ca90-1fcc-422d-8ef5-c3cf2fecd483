<template>
  <!-- 日均计件统计表 -->
  <content-panel>
    <template v-slot:search>
      <search-box class="search-box">
        <el-form size="mini" :inline="true" :model="searchForm" ref="searchForm" label-position="right">
          <el-form-item label="核算工厂:" prop="factoryId">
            <el-select v-model="searchForm.factoryId" filterable placeholder="请选择工厂" @change="onSearch" clearable>
              <el-option v-for="item in tabList" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="核算大工序" prop="bigProcessId">
            <el-select v-model="searchForm.bigProcessId" filterable clearable placeholder="请选择大工序" @change="onSearch">
              <el-option v-for="item in process.bigProcessList" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="核算月份：" prop="accountingMonth">
            <el-date-picker @change="onSearch" v-model="searchForm.accountingMonth" type="month" placeholder="请选择日期"
              :clearable="false" :picker-options="pickerOptions">
            </el-date-picker>
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button size="small" type="primary" @click="onSearch">
            查询</el-button>
          <el-button size="small" type="warning" @click="resetSearchForm">
            重置</el-button>
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <div class="header_tableName">{{ filterName }}</div>
      <template v-slot:header-right>
        <el-button size="small" type="primary" @click="handleExport"> 导出 </el-button>
      </template>
      <el-table key="tableKey" border stripe v-loading="loading" ref="tableRef" highlight-current-row :data="tableData"
        :height="maxTableHeight" :span-method="objectSpanMethod">

        <!-- 渲染处理后的表头 -->
        <template v-for="(item, index) in processedColumns">
          <!-- 如果是普通列 -->
          <el-table-column v-if="!item.columns" :key="item.fieldName || index" :prop="item.fieldName"
            :label="item.columnName" :width="item.width" :fixed="item.fixed" align="center" show-overflow-tooltip>
            <template slot="header">
              <div v-if="item.columnName && item.columnName.includes('第') && item.columnName.includes('周')"
                v-html="formatWeekText(item.columnName)">
              </div>
              <div v-else="item.columnName && typeof item.columnName === 'string' &&
                item.columnName.includes('第') && item.columnName.includes('周')"
                v-html="formatWeekText(item.columnName)">
              </div>
            </template>
            <template slot-scope="{ row }">
              <div v-if="['factoryName', 'bigProcessCode', 'bigProcessName'].includes(item.fieldName)">
                {{ row[item.fieldName] }}
              </div>
              <span v-else>{{ filterDouble(row[item.fieldName]) }}</span>
            </template>

          </el-table-column>

          <!-- 如果是分组列（历史月份） -->
          <el-table-column v-else :key="'group-' + index" :label="item.label" align="center"
            class-name="history-month-header">
            <el-table-column v-for="column in item.columns" :key="column.fieldName" :prop="column.fieldName"
              :label="column.columnName" :width="column.width" align="left" show-overflow-tooltip>
              <template slot-scope="{ row }">
                <span>{{ filterDouble(row[column.fieldName]) }}</span>
              </template>
            </el-table-column>
          </el-table-column>
        </template>
      </el-table>
      <template v-slot:footer>
        <div style="text-align: right">
          <el-pagination @size-change="onSizeChange" @current-change="onNumChange" :current-page="pageNum"
            :page-size="pageSize" :total="total" :page-sizes="[50, 100, 200]"
            layout="total, sizes, prev, pager, next, jumper">
          </el-pagination>
        </div>
      </template>
    </table-panel>
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import moment from "moment";
import { moneyFormat, calculateTableWidth } from "@/utils";
export default {
  name: "PlateTypeDailyPieceworkQuery",
  mixins: [tableMixin, pagePathMixin],
  data() {
    return {
      searchForm: {
        factoryId: "",
        accountingMonth: moment().format("YYYY-MM"),
        bigProcessId: "",
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      tabList: [],
      tableData: [], // 表格数据
      loading: false,
      pageSize: 50,
      pageNum: 1,
      total: 100,
      filterParam: {},
      tableKey: Math.random(),
      columnList: [], // 原始表头数据
      processedColumns: [], // 处理后的表头数据（包含分组信息）
      rowSpans: [],
    };
  },
  async created() {
    await this.getFactory();
    this.onSearch();
  },
  computed: {
    process() {
      return {
        bigProcessList:
          (this.searchForm.factoryId &&
            this.tabList.find((item) => item.id == this.searchForm.factoryId)
              .bigProcess) ||
          [],
      };
    },
    filterName() {
      let accountingMonth = this.searchForm.accountingMonth;
      return `${moment(accountingMonth).format("YYYY年MM月")} 板木分厂日均计件统计表`;
    },
  },
  filters: {
    filterData(value) {
      return !value ? "-" : moneyFormat(value);
    },
  },
  watch: {
    tableData: {
      handler() {
        this.updateRowSpans();
      },
      deep: true,
    },
  },
  methods: {
    formatWeekText(text) {
      if (!text || typeof text !== 'string') return text;

      if (text.includes('第') && text.includes('周')) {
        const weekIndex = text.indexOf('周');
        const weekPart = text.substring(0, weekIndex + 1);
        const datePart = text.substring(weekIndex + 1);

        return `<div class="week-text-container">
          <div>${weekPart}</div>
          <div>${datePart}</div>
        </div>`;
      }

      return text;
    },

    // 获取工厂
    getFactory() {
      return this.$api.plateTypeSystemManage.getBasicPermission
        .getBasicPermissionAll({ moduleId: 3 })
        .then(({ data }) => {
          this.tabList = data || [];
        });
    },
    //获取表头
    getHeader() {
      return this.$api.plateTypePieceWageSystem.dailyPieces
        .getDailyPieceHeader({
          ...this.filterParam
        })
        .then((res) => {
          const originalColumns = res.data;
          const currentMonthIndex = originalColumns.findIndex(col => col.fieldName === 'currentMonth');
          const lastMonthAvgIndex = originalColumns.findIndex(col => col.fieldName === 'lastMonthAvg');

          let newColumns = [];
          if (currentMonthIndex !== -1 && lastMonthAvgIndex !== -1 && currentMonthIndex < lastMonthAvgIndex - 1) {
            newColumns = [...originalColumns.slice(0, currentMonthIndex + 1)];
            const historyMonthsGroup = {
              label: '近6个月日均计件工资',
              columns: originalColumns.slice(currentMonthIndex + 1, lastMonthAvgIndex)
            };
            newColumns.push(historyMonthsGroup);
            newColumns = [...newColumns, ...originalColumns.slice(lastMonthAvgIndex)];
          } else {
            newColumns = originalColumns;
          }
          this.columnList = originalColumns;
          this.processedColumns = newColumns;

          // Apply width calculations to columns
          let items = {
            factoryName: "100",
            bigProcessName: "100",
            bigProcessCode: "100",
            lastMonthAvg: "160",
            diffRate: "260",
          };
          this.columnList = this.columnList.map((item) => {
            if (Object.keys(items).includes(item.fieldName)) {
              Object.keys(items).forEach((key) => {
                if (key == item.fieldName) {
                  item.width = items[item.fieldName];
                }
              });
            } else {
              item.width = this.flexWidth(
                item.fieldName,
                this.tableData,
                item.columnName
              );
            }
            if (["核算工厂", "大工序名称", "大工序编码"].includes(item.columnName)) {
              item.fixed = "left";
            }
            return item;
          });

          // Apply the same width settings to processed columns
          this.processedColumns = this.processedColumns.map(item => {
            // For regular columns
            if (!item.columns) {
              const originalColumn = this.columnList.find(col => col.fieldName === item.fieldName);
              if (originalColumn && originalColumn.width) {
                item.width = originalColumn.width;
              }
              if (originalColumn && originalColumn.fixed) {
                item.fixed = originalColumn.fixed;
              }
            }
            // For grouped columns (history months)
            else if (item.columns) {
              item.columns = item.columns.map(subItem => {
                const originalColumn = this.columnList.find(col => col.fieldName === subItem.fieldName);
                if (originalColumn && originalColumn.width) {
                  subItem.width = originalColumn.width;
                }
                return subItem;
              });
            }
            return item;
          });

          // Check if we need to remove width constraints
          if (this.$refs.tableRef && this.$refs.tableRef.$el) {
            let totalWidth = this.columnList.reduce((pre, cur) => {
              return (pre += Number(cur.width || 0));
            }, 0);

            if (totalWidth <= this.$refs.tableRef.$el.clientWidth) {
              // Keep width for diffRate column but remove for others
              this.columnList.forEach((item) => {
                if (item.fieldName !== 'diffRate') {
                  delete item.width;
                }
              });

              // Apply the same to processed columns
              this.processedColumns = this.processedColumns.map(item => {
                if (!item.columns && item.fieldName !== 'diffRate') {
                  delete item.width;
                } else if (item.columns) {
                  item.columns = item.columns.map(subItem => {
                    if (subItem.fieldName !== 'diffRate') {
                      delete subItem.width;
                    }
                    return subItem;
                  });
                }
                return item;
              });
            }
          }
        });
    },

    //计算表格列宽度
    flexWidth(...args) {
      return calculateTableWidth(...args);
    },
    // 获取日均计件统计表
    async getList() {
      this.loading = true;
      await this.getHeader();

      this.$api.plateTypePieceWageSystem.dailyPieces
        .getDailyPieceList({
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          ...this.filterParam
        })
        .then(async (res) => {


          this.tableData = res.data || [];



          this.updateRowSpans();
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.filterParam = {};
      this.searchForm.factoryId = "";
      this.onSearch();
    },
    onSizeChange(val) {
      this.pageSize = val;
      this.pageNum = 1;
      this.getList();
    },
    onNumChange(val) {
      this.pageNum = val;
      this.getList();
    },
    // 搜索
    onSearch() {
      let month = this.searchForm.accountingMonth;
      this.searchForm.accountingMonth = month
        ? month
        : moment().format("YYYY-MM");
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (key == "accountingMonth" && val) {
          this.filterParam.accountingMonth = moment(val).format("YYYY-MM");
        } else if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      this.pageNum = 1;
      this.getList();
    },
    filterDouble(value) {
      if (!value) return '-';
      return moneyFormat(value);
    },
    //导出
    handleExport() {
      let params = {};
      if (Object.keys(this.filterParam).length) {
        params = {
          ...this.filterParam,
        };
      }
      this.$api.plateTypePieceWageSystem.dailyPieces
        .exportDailyPieceList({
          ...params,
          ...this.params,
        })
        .then((res) => {
          if (res.code == 200) {
            this.$message.success("导出操作成功，请前往导出记录查看详情");
          }
        });
    },
    // 初始化或更新 rowSpans
    updateRowSpans() {
      this.rowSpans = [];
      if (this.tableData.length === 0) return;
      let currentFactoryName = null;
      let currentRowspan = 0;
      for (let i = 0; i < this.tableData.length; i++) {
        const factoryName = this.tableData[i].factoryName;

        if (factoryName !== currentFactoryName) {
          if (currentRowspan > 0) {
            this.rowSpans.push({ index: i - currentRowspan, rowspan: currentRowspan });
          }
          currentFactoryName = factoryName;
          currentRowspan = 1;
        } else {
          currentRowspan++;
        }
      }
      if (currentRowspan > 0) {
        this.rowSpans.push({ index: this.tableData.length - currentRowspan, rowspan: currentRowspan });
      }
    },
    objectSpanMethod({ row, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        if (this.tableData.length === 0) {
          return { rowspan: 0, colspan: 0 };
        }

        const factoryName = row.factoryName;
        if (rowIndex === 0 || this.tableData[rowIndex - 1].factoryName !== factoryName) {
          const spanInfo = this.rowSpans.find(span => span.index === rowIndex);
          return spanInfo ? { rowspan: spanInfo.rowspan, colspan: 1 } : { rowspan: 1, colspan: 1 };
        } else {
          return { rowspan: 0, colspan: 0 };
        }
      }
    }
  },
};
</script>

<style lang="stylus" scoped>
ellipsis() {
  font-size: 16px;
  font-weight: 600;
}

.header_tableName {
  ellipsis();
  font-size: 22px;
  text-align: center;
  margin-bottom: 8px;
}
>>>.el-table th div{
  line-height: 22px
}
>>>.el-table__cell {
  .cell {
    width: 100% !important;
    padding: 0 8px !important;
  }
}

>>>.el-table__fixed {
  height: auto !important;
  bottom: 17px !important;
}

>>>.week-text-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items:center;
}
>>>.week-text-container div{
  line-height:18px!important
}

>>>.history-month-header {
  min-width: 120px;
}

>>>.el-table__header th {
  min-width: 80px;
}

</style>