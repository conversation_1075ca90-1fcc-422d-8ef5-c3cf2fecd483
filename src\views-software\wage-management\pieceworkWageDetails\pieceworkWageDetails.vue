<template>
  <content-panel class="panel-tabs">
    <el-row class="tabs-row">
      <el-col :span="22">
        <el-tabs v-model="activeTab" class="tabs">
         <el-tab-pane v-for="tab in getTabPermission" :key="tab.name" :name="tab.name" :label="tab.label"></el-tab-pane>
        </el-tabs>
      </el-col>
      <el-col :span="2" class="table-btn-area" v-show="activeTab&& activeTab !== 'defaultNmae'">
        <el-button type="primary" size="small" v-show="activeTab === 'PieceworkWage'"
          @click="updateView">刷新</el-button>
        <el-button type="primary" size="small" @click="handleExport">导出</el-button>
      </el-col>
    </el-row>
     <component :is="activeTab" ref="SortwarePieceworkWageRef"></component>
    <updatePieceworkWageDialog ref="updatePieceworkWageDialogRef" v-if="updateVisible" @confirm="onUpdateConfirm"
      @cancel="onUpdateCancel" :factoryList="tabList" :visible="updateVisible">
    </updatePieceworkWageDialog>
  </content-panel>
</template>

<script>
import {getPermitList} from '@/store/modules/permission'
import GroupDetail from './components/groupDetail.vue';
import PersonalDetails from './components/personalDetails.vue';
import PieceworkWage from './components/pieceworkWage.vue';
import updatePieceworkWageDialog from "./components/updatePieceworkWageDialog.vue";
import pieceworkWageSummary from "./components/pieceworkWageSummary.vue"; 
import defaultNmae from "./components/defaultNmae.vue"; 
const tabPermissions=[
  {
    name: 'PieceworkWage',
    label: '计件工资',
    permission: 'was-customized$pieceworkWage$software$SoftwarePieceworkWageDetails$pieceworkWage'
  },
  {
    name: 'PersonalDetails',
    label: '个人明细',
    permission:  'was-customized$pieceworkWage$software$SoftwarePieceworkWageDetails$personalDetails'
  },
  {
    name: 'GroupDetail',
    label: '集体明细', 
    permission: 'was-customized$pieceworkWage$software$SoftwarePieceworkWageDetails$groupDetail'
  },
   {
    name: 'pieceworkWageSummary',
     label: '集体计件汇总', 
    permission: 'was-customized$pieceworkWage$software$SoftwarePieceworkWageDetails$pieceworkWageSummary'
  }
] 
export default {
  name: 'SoftwarePieceworkWageDetails',
  components: {
    GroupDetail,
    PersonalDetails,
    PieceworkWage,
    updatePieceworkWageDialog,
    pieceworkWageSummary,
    defaultNmae
  },
  data() {
    return {
      activeTab: 'defaultNmae',
      updateVisible: false, 
      tabList: []
    };
  },
  created() {
    this.$api.softwareSystemManage.getBasicPermission.getBasicPermissionAll().then((res) => {
      this.tabList = res.data.map((item) => ({
        label: item.name,
        name: item.name,
        id: item.id,
      }));
    });
  }, 
  watch: { 
     getTabPermission: {
        handler(newVal) { 
          if (newVal.length > 0 && this.activeTab=='defaultNmae') {
          this.activeTab = newVal[0].name;
        } 
      },
      deep: true,
      immediate: true,
    },
},
  computed: {
    // 获取当前tab的权限
    getTabPermission() {
      const permitList = getPermitList();  
      return tabPermissions.filter(tab => permitList.includes(tab.permission));
    },
  },
  methods: {
    //导出
    handleExport() {
      let exportForm = JSON.parse(JSON.stringify(this.$refs.SortwarePieceworkWageRef.filterParam));
      this.$api.common
        .doExport(this.activeTab == 'PieceworkWage' ? 'plankPieceWageSystemExport' : this.activeTab == 'GroupDetail' ? 'plankPieceWageGroup' : this.activeTab == 'PersonalDetails' ? 'plankPieceWagePerson' : 'plankPieceWageSystemTotalExport', { ...exportForm })
        .then((res) => {
          if (res.code == 200) {
            this.$message.success("导出操作成功，请前往导出记录查看详情");
          }
        });
    },
    //刷新
    updateView() {
      this.updateVisible = true;
    },
    onUpdateCancel() {
      this.updateVisible = false;
    },
    onUpdateConfirm(data) {
      this.$api.softwarePieceWageSystem
        .updateMesPieceWage({
          ...data
        }).then((res) => {
          this.updateVisible = false;
          this.$refs.SortwarePieceworkWageRef.onSearch();
          this.$message.success("刷新成功");
        }).finally(() => {
          if (this.updateVisible) {
            this.$refs.updatePieceworkWageDialogRef.isLoading = false;
          }
        });
    },
  },
};
</script>

<style lang="stylus" scoped>
>>>.table-panel-footer-top {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .table_footer {
    overflow: auto;

    >ul {
      display: flex;
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        white-space: nowrap
        padding: 0 10px;
      }
    }
  }
}
.panel-tabs
    position: relative;
.panel-tabs
  >>> .main-area
    padding-top 0
.tabs-row
    display:flex;
    align-items:center;
.tabs-row:after
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 2px;
    background-color: #E4E7ED;
    z-index: 1;
.table-btn-area
    display:flex;
    justify-content:flex-end;
    margin-right:12px;
.tabs
  >>> .el-tabs__header
    margin-bottom 5px
    .el-tabs__nav-wrap::after{
      display:none;
    }
</style>
