<template>
  <content-panel>
    <template v-slot:search>
      <search-box class="search-box">
        <el-form size="mini" :inline="true" :model="searchForm" ref="searchForm" label-width="90px" class="rangeTime"
          label-position="right">
          <el-form-item label="核算工厂:" prop="factoryId">
            <el-select v-model="searchForm.factoryId" filterable clearable placeholder="请选择核算工厂"
              @change="onChangeFactory">
              <el-option v-for="item in tabList" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="核算班组都:" prop="processId">
            <el-select v-model="searchForm.processId" filterable clearable placeholder="请选择核算班组"
              @change="onChangeProcess">
              <el-option v-for="item in groupList" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="员工姓名:" prop="staffName">
            <el-input clearable v-model.trim="searchForm.staffName" size="mini" @keyup.enter.native="onSearch">
              <template slot="append">
                <search-batch @seachFilter="onSearch('staffNames', $event)"
                  @focusEvent="focusEvent('staffNames', $event)" ref="childrenStaffNames" titleName="员工姓名" />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="厂牌编号:" prop="staffCode">
            <el-input v-model.trim="searchForm.staffCode" size="mini" clearable @keyup.enter.native="onSearch">
              <template slot="append">
                <search-batch @seachFilter="onSearch('staffCodes', $event)"
                  @focusEvent="focusEvent('staffCodes', $event)" ref="childrenStaffCodes" titleName="厂牌编号" />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item class="range" label="核算月份:" prop="accountingMonth">
            <el-date-picker :clearable="false" v-model="searchForm.startMonth" value-format="yyyy-MM" type="month"
              placeholder="开始月份" clearable>
            </el-date-picker>
            <span class="separator">至</span>
            <el-date-picker :clearable="false" v-model="searchForm.endMonth" value-format="yyyy-MM" type="month"
              placeholder="结束月份" clearable>
            </el-date-picker>
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button size="small" type="primary" @click="onSearch">
            查询
          </el-button>
          <el-button size="small" type="warning" @click="resetSearchForm">
            重置
          </el-button>
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <el-table stripe border v-loading="loading" ref="tableRef" highlight-current-row :height="maxTableHeight"
        :data="tableData" key="tableKey">
        <el-table-column prop="factoryName" label="核算工厂" width="100" fixed align="left">
        </el-table-column>
        <el-table-column prop="staffName" label="员工姓名" width="100" fixed align="left">
        </el-table-column>
        <el-table-column prop="staffCode" label="厂牌编号" fixed align="left">
        </el-table-column>
        <el-table-column prop="accountingMonth" label="核算月份" align="left">
        </el-table-column>
        <el-table-column prop="processName" label="核算班组" align="left">
        </el-table-column>
        <el-table-column prop="personPiece" label="个人计件" align="left">
          <template slot-scope="{ row }">
            {{ row.personPiece | filterData }}
          </template>
          <template slot="header">
            <span>个人计件</span>
            <el-tooltip placement="top">
              <div slot="content">
                MES系统推送的个人计件工资汇总。
              </div>
              <i class="el-icon-question"></i>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="groupPiece" label="集体计件" align="left">
          <template slot-scope="{ row }">
            {{ row.groupPiece | filterData }}
          </template>
          <template slot="header">
            <span>集体计件</span>
            <el-tooltip placement="top">
              <div slot="content">
                MES系统推送的集体计件工资，按规则分摊至个人的工资。
              </div>
              <i class="el-icon-question"></i>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="totalPiece" label="总计件工资" align="left">
          <template slot-scope="{ row }">
            {{ row.totalPiece | filterData }}
          </template>
          <template slot="header">
            <span>总计件工资</span>
            <el-tooltip placement="top">
              <div slot="content">
                总计件工资=个人计件+集体计件。
              </div>
              <i class="el-icon-question"></i>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="left" width="100">
          <template slot-scope="scope">
            <el-button type="text" size="mini" @click="handleLook(scope.$index, scope.row)">
              查看明细</el-button>
          </template>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <div class="table_footer">
          <ul>
            <li>
              <span>个人计件:</span><span>{{ pieceworkWageInfo.personPiece | filterDataPiece }}</span>
            </li>
            <li>
              <span>集体计件:</span><span>{{ pieceworkWageInfo.groupPiece | filterDataPiece }}</span>
            </li>
            <li>
              <span>总计件:</span><span>{{ pieceworkWageInfo.totalPiece | filterDataPiece }}</span>
            </li>
          </ul>
        </div>
        <div style="text-align: right">
          <el-pagination @size-change="onSizeChange" @current-change="onNumChange" :current-page="pageNum"
            :page-size="pageSize" :total="total" :page-sizes="[50, 100, 200]"
            layout="total, sizes, prev, pager, next, jumper">
          </el-pagination>
        </div>
      </template>
    </table-panel>
    <PieceworkWageDialog @cancel="onCancel" :details="details" :visible="detailsVisible"></PieceworkWageDialog>

  </content-panel>
</template>

<script>
import moment from "moment";
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import { moneyFormatZh } from "@/utils";
import PieceworkWageDialog from "./pieceworkWageDialog.vue";
export default {
  name: "plateTypePieceworkWage",
  mixins: [tableMixin, pagePathMixin],
  components: {
    PieceworkWageDialog
  },
  data() {
    return {
      searchForm: {
        staffCode: "",
        processId: '',
        staffName: "",
        factoryId: "",
        accountingMonth: "",
        startMonth: moment().subtract(1, "month").format("YYYY-MM"),
        endMonth: moment().subtract(1, "month").format("YYYY-MM")
      },
      tabList: [],
      columnList: [],
      filterParam: {},
      groupList: [],
      pieceworkWageInfo: {},
      detailsVisible: false,//计件工资明细
      params: {},
      details: {},
      tableData: [],
      loading: false,
      resizeOffset: 55,
      pageSize: 50,
      pageNum: 1,
      total: 0,
      tableKey: "",
    };
  },
  created() {
    this.$api.plateTypeSystemManage.getBasicPermission.getBasicPermissionAll().then((res) => {
      this.tabList = res.data.map((item) => ({
        label: item.name,
        name: item.name,
        id: item.id,
      }));
    });
    this.onSearch();
  },
  filters: {
    filterData(value) {
      return !value ? "-" : moneyFormatZh(value);
    },
    filterDataPiece(value) {
      return !value ? "0" : moneyFormatZh(value);
    }
  },
  methods: {

    //统计
    getAccount() {
      this.$api.plateTypePieceWageSystem.getMesPieceWagetStatistic({
        ...this.filterParam,
        ...this.params,
      }).then((res) => {
        this.pieceworkWageInfo = res.data || {};
      });
    },
    onChangeFactory() {
      this.onSearch();
      this.$api.plateTypeSystemManage.getBasicPermission.getBasicGroupList(this.searchForm.factoryId).then((res) => {
        this.groupList = res.data.map((item) => ({
          label: item.name,
          name: item.name,
          id: item.id,
        }));
      });
    },
    onChangeProcess() {
      this.onSearch();
    },

    onCancel() {
      this.detailsVisible = false;
    },
    //查看明细
    handleLook(index, data) {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        target: '.wage-details',
        spinner: 'el-icon-loading',
        background: 'rgba(255, 255, 255s, 1)'
      });
      this.$api.plateTypePieceWageSystem.getMesPieceWagetDetails({
        accountingMonth: data.accountingMonth,
        factoryId: data.factoryId,
        staffCode: data.staffCode,
        staffName: data.staffName
      }).then((res) => {
        this.details = res.data || {};
      }).finally(() => {
        loading.close();
      });

      this.detailsVisible = true;
    },
    //获取列表
    getList() {
      this.getAccount();
      this.loading = true;
      this.$api.plateTypePieceWageSystem
        .getMesPieceWagetList({
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          filterData: {
            ...this.filterParam,
            ...this.params,
          },
        })
        .then(({ data: { list, total } }) => {
          this.tableData = list;
          this.total = total;
          this.tableKey = Math.random();
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.$refs.childrenStaffNames.resetFilter();
      this.$refs.childrenStaffCodes.resetFilter();
      this.filterParam = {};
      this.searchForm.startMonth = moment().subtract(1, "month").format("YYYY-MM");
      this.searchForm.endMonth = moment().subtract(1, "month").format("YYYY-MM");
      this.params = {};
      this.onSearch();

    },
    //搜索
    onSearch(name, data) {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (key == 'startMonth' && val) {
          this.filterParam.startMonth = moment(val).format('YYYY-MM') || '';
        } else if (key == 'endMonth' && val) {
          this.filterParam.endMonth = moment(val).format('YYYY-MM') || '';
        } else if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.filterParam[name] = data;
      }
      this.pageNum = 1;
      this.getList();
    },
    focusEvent(name, data) {
      if (Array.isArray(data) && !data.length) {
        this.params[name] = [];
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.params[name] = data;
      }
    },
    onSizeChange(val) {
      this.pageSize = val;
      this.pageNum = 1;
      this.getList();
    },
    onNumChange(val) {
      this.pageNum = val;
      this.getList();
    },
  },
};
</script>

<style lang="stylus" scoped>
ul {
  list-style: none;
  display: flex;
  align-items: center;
  padding: 0;
  margin: 0;
  margin-top:10px
  li{
    margin-right: 10px;
  }
}
</style>