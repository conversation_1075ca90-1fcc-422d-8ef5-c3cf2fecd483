import { getPermitList } from '@/store/modules/permission';
import { SUB_APP_CODE, SUB_APP_LOGISTICS_CODE } from '@/api/index.js';

export default {
  inserted(el, binding, vnode) {
    const { value } = binding;
    const permits = getPermitList() || [];
    console.log(SUB_APP_LOGISTICS_CODE, SUB_APP_CODE);
    // 支持常量替换：将 ${SUB_APP_CODE} 替换为实际值
    let permissionValue = value;
    if (typeof value === 'string' && value.includes('was-customized')) {
      permissionValue = value.replace(/was-customized\$\w+/g, SUB_APP_LOGISTICS_CODE);
    }
    console.log('权限拦截', value.includes('was-customized'), permissionValue);
    const hasPermission = permits.some(permit => {
      return permit.indexOf(permissionValue) > -1;
    });

    if (!hasPermission) {
      el.parentNode && el.parentNode.removeChild(el);
    }
  }
};

export function checkHasPermission(permission) {
  const permits = getPermitList() || [];

  // 支持常量替换：将 ${SUB_APP_CODE} 替换为实际值
  let permissionValue = permission;
  if (typeof permission === 'string' && permission.includes('was-customized')) {
    permissionValue = permission.replace(/was-customized\$\w+/g, SUB_APP_LOGISTICS_CODE);
  }

  return permits.includes(permissionValue);
}