const fs = require('fs');

// 读取文件
const filePath = 'src/api/software-api.js';
let content = fs.readFileSync(filePath, 'utf8');

content =content.replace(/\/\${SUB_APP_CODE}\//g, '/${SUB_APP_SOFTWARE_CODE}/'); 
// 将所有包含 /${SUB_APP_CODE}/ 的双引号字符串改为模板字符串
// content = content.replace(/"\/\$\{SUB_APP_CODE\}\/([^"]*)"/g, '`/${SUB_APP_CODE}/$1`');

// 写回文件
fs.writeFileSync(filePath, content, 'utf8');

console.log('正确修复完成！');