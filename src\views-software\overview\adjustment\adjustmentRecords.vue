<template>
  <qDialog
    :visible="visible"
    title="调整记录"
    :innerScroll="false"
    :innerHeight="500"
    :showFooter="false"
    width="800px"
    @cancel="handleCancel"
    :before-close="handleCancel"
  >
    <el-table
      stripe
      border
      v-loading="loading"
      highlight-current-row
      :height="300"
      :data="tableData"
    >
      <el-table-column label="调整前实发" align="center" show-overflow-tooltip>
        <template slot-scope="{ row }">
          {{ row.beforeAmount | moneyFormat }}
        </template>
      </el-table-column>
      <el-table-column label="本月余留" align="center" show-overflow-tooltip>
        <template slot-scope="{ row }">
          {{ row.remainAmount | moneyFormat }}
        </template>
      </el-table-column>
      <el-table-column label="本月划入" align="center" show-overflow-tooltip>
        <template slot-scope="{ row }">
          {{ row.incomeAmount | moneyFormat }}
        </template>
      </el-table-column>
      <el-table-column label="调整后实发" align="center" show-overflow-tooltip>
        <template slot-scope="{ row }">
          {{ row.afterAmount | moneyFormat }}
        </template>
      </el-table-column>
      <el-table-column
        label="修改人员"
        prop="staffName"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        label="修改时间"
        prop="updateTime"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
    </el-table>
  </qDialog>
</template>

<script>
import moment from "moment";
export default {
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    editForm: Object,
  },
  data() {
    return {
      tableData: [],
      loading: false,
    };
  },
  created() {
    this.getAdjustmentRecord();
  },
  methods: {
    //分厂调整记录
    getAdjustmentRecord() {
      this.loading = true;
      const { factoryId, accountingMonth } = JSON.parse(this.$Base64.decode(this.$route.query.data));
      this.$api.softwareWorkbench
        .adjustmentRecord({
          factoryId,
          accountingMonth,
          ...this.editForm,
        })
        .then(({ data }) => {
          this.tableData =
            data.map((item) => ({
              ...item,
              updateTime: moment(item.updateTime).format("YYYY-MM-DD HH:mm"),
            })) || [];
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleCancel() {
      this.$emit("adjustmentCancel", "cancel");
    },
  },
};
</script>

<style lang="stylus" scoped>
.header_top {
  > span {
    display: inline-block;
    width: 30%;
  }
}

.content {
  padding: 0;

  li {
    width: 30%;
    display: inline-block;
    height: 40px;
    line-height: 40px;

    .el-input {
      width: 80%;
    }
  }
}
</style>
