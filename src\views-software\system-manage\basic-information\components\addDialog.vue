<template>
<content-panel>
  <q-dialog
    :visible="isVisible"
    :innerScroll="false"
    :title="title"
    width="500px"
    :isLoading="isLoading"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel">
    <el-form :model="addForm"
      :rules="rules"
      ref="addForm"
      label-width="108px"
      size="small">
      <el-form-item
        label="核算班组:"
        prop="groupName"
        >
        <el-input
          v-model.trim="addForm.groupName"
          clearable
          placeholder="请输入核算班组"
          maxlength="30"
          show-word-limit>
        </el-input>
      </el-form-item>
      <el-form-item
        label="核算大工序:"
        prop="parentId">
        <el-select
          v-model="addForm.parentId"
          filterable clearable
          placeholder="请选择核算大工序"
          @change="onBigProcessChange"
          >
          <el-option
            v-for="item in bigGroupList"
            :key="item.id"
            :label="item.name"
            :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        label="小工序名称:"
        prop="mesProcessNames">
        <el-input
              v-model.trim="addForm.mesProcessNames"
              clearable
              disabled
              ype="text"
              placeholder="请选择小工序">
            <template
              slot="append" >
              <el-button @click="selectBigProcessCode">选择</el-button>
            </template>
          </el-input>
      </el-form-item>
      <el-form-item
        label="启用状态:"
        prop="enable">
        <el-switch
          inactive-color="#ff4949"
          v-model="addForm.enable"
          :active-text="addForm.enable == '1' ? '启用' : '禁用'"
          :active-value="1"
          :inactive-value="0">
        </el-switch>
      </el-form-item>
      <el-form-item
        label="备注说明:"
        label-position="top">
        <el-input
          type="textarea"
          resize="none"
          rows="5"
          maxlength="300"
          show-word-limit
          v-model.trim="addForm.remark">
        </el-input>
      </el-form-item>
    </el-form>
  </q-dialog>
  <AddBigProcessCode
      v-if="isBigCodevisible"
      :visible.sync="isBigCodevisible"
      :factoryCode="addForm.factoryCode"
      :bigProcessCode="bigProcessCode"
      :checkData="mesProcessList"
      @confirmProcessIds="confirmProcessIds"
    />
</content-panel>
</template>

<script>
import AddBigProcessCode from "./addBigProcessCode.vue";
export default {
  name: "addDialog",
  components: { AddBigProcessCode },
  props: {
    isVisible: {
      type: Boolean,
      required: true
    },
    title: {
      type: String,
      required: true
    },
    editForm: Object
  },
  data() {
    return {
      addForm: {
        groupName: "",
        parentId:"",
        mesProcessIdList:[],
        mesProcessNames:'',
        mesProcessCode: "",  // 记录选择的小工序
        enable: 1,
        remark: "",
        treeLevel:3,
      },
      isBigCodevisible: false,
      bigProcessCode:'',//大工序编码
      isLoading: false,
      mesProcessList:[],
      isLoading: false,
      rules:{
        groupName: [{ required: true, message: '请输入核算班组', trigger: 'blur' }],
        parentId: [{ required: true, message: '请选择核算大工序', trigger: 'change' }],
        enable: [{ required: true, message: '请选择启用状态', trigger: 'change' }],
      },
      bigGroupList: [],
      dataList:[],
    }
  },
  async created() {
    await this.getBigGroupList()
    this.addForm = {
      ...this.addForm,
      ...this.editForm
    }
  },
  methods: {
      //选择小工序
    selectBigProcessCode() {
      if (!this.addForm.parentId) {
        this.$message.error("请先选择核算大工序");
        return;
      }
      this.isBigCodevisible=true
    },
    confirmProcessIds(data) {
      let names = data.map((v) => {
        return v.name;
      });
      this.addForm.mesProcessNames = names.join(",");
      this.addForm.mesProcessIdList=data.map((v) => {
        return v.id;
      });
      this.mesProcessList =data
    },
   //获取大工序列表
    getBigGroupList() {
      return this.$api.softwareSystemManage.getBasicPermission
        .getQuFactory({moduleId:4})
        .then(({ data }) => {
          this.dataList=data
          this.bigGroupList = data.find((item) => item.id === this.editForm.factoryId).bigProcess || [];

          const currentProcess = this.bigGroupList.find((item) => item.id === this.editForm.parentId) || {};
          this.bigProcessCode =  currentProcess.code

          if(this.title == "编辑"){
            const lists = data.find((item) => item.id === this.editForm.factoryId).process || [];
            this.mesProcessList = lists.find((item) => item.id === this.editForm.id).mesProcessList || [];
            this.addForm.mesProcessNames = this.mesProcessList.map(v => v.name).join(",");
            this.addForm.mesProcessIdList = this.mesProcessList.map(v => v.id)
          }
        });
    },
    onBigProcessChange() {
      const currentProcess = this.bigGroupList.find((item) => item.id === this.addForm.parentId) || {};
      this.bigProcessCode =  currentProcess.code
      this.addForm.mesProcessNames = "";
      this.addForm.mesProcessIdList = [];
    },
    handleCancel() {
      this.$emit('cancel', 'cancel')
    },
    handleConfirm() {
      this.$refs.addForm.validate(valid => {
        if (!valid) return
        this.isLoading = true
        if (this.title == "新增") {
        this.$api.softwareSystemManage.getBasicPermission.addGroup({ ...this.addForm }).then(({ success }) => {
          if (success)
            this.$notify({
              title: '成功',
              message: '新增成功',
              type: 'success'
            });
            this.$emit('cancel', 'cancel')
            this.$bus.$emit('softwareAddCancel', 'confirm')
          }).finally(() => {
          this.isLoading = false;
          })
      } else {
        this.$api.softwareSystemManage.getBasicPermission.editGroup(this.addForm).then(({ success }) => {
          if (success)
            this.$notify({
              title: '成功',
              message: '修改成功',
              type: 'success'
            });
          this.$emit('cancel', 'cancel')
          this.$bus.$emit('softwareAddCancel', 'confirm')
        }).finally(() => {
          this.isLoading = false;
        })
      }
      })
    }
  }
}
</script>

<style lang="stylus" scoped>
>>>.el-textarea__inner {
  padding-right: 50px;
}

.el-textarea {
  >>>.el-input__count {
    right: 20px !important;
  }
}
>>>.el-input-group__append button.el-button{
  color: #fff;
  background-color: #0bb78e;
  border: 4px solid #0bb78e;
  cursor:pointer
}
</style>
