<template>
  <!-- 定制人工效率分析表 -->
  <content-panel>
    <template v-slot:search>
      <search-box class="search-box">
        <el-form size="mini" :inline="true" :model="searchForm" ref="searchForm" label-position="right">
          <el-form-item label="核算工厂:" prop="factoryId">
            <el-select v-model="searchForm.factoryId" filterable placeholder="请选择工厂" @change="onSearch">
              <el-option v-for="item in tabList" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="核算月份:" prop="accountingMonth">
            <el-date-picker @change="onSearch" v-model="searchForm.accountingMonth" type="month" placeholder="请选择日期"
              :clearable="false" :picker-options="pickerOptions">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="人效工序:" prop="laborEffectId">
            <el-select v-model="searchForm.laborEffectId" filterable clearable placeholder="请选择核算大工序" @change="onSearch">
              <el-option v-for="item in bigProcessList" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item> 
        </el-form>
        <template v-slot:right>
          <el-button size="small" type="primary" @click="onSearch">
            查询</el-button>
          <el-button size="small" type="warning" @click="resetSearchForm">
            重置</el-button>
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <template v-slot:header-right>
        <el-button size="small" type="primary" @click="handleExport">
          导出</el-button>
      </template>
      <div class="header_tableName">生产财务本部定制分厂人工效率分析表</div>
      <el-table stripe border v-loading="loading" ref="tableRef" highlight-current-row :data="tableData"
        :height="maxTableHeight" :span-method="objectSpanMethod">
        <el-table-column prop="factoryName" align="center" label="分厂" width="150" fixed show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="laborEffectName" align="center" label="人效工序" width="150" fixed show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          :label="this.filterAmountto"
          align="center"
          class-name="customOne"
        >
          <el-table-column
            prop="averagePersonSum"
            class-name="customOne"
            min-width="105"
            align="center"
            label="平均人数"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="totalWorkDaySum"
            class-name="customOne"
            label="本厂出勤"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="backManDaysSum"
            class-name="customOne"
            label="外厂/部出勤"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="totalAttendanceSum"
            class-name="customOne"
            label="出勤合计"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="totalOutputSum"
            class-name="customOne"
            label="总产量"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column> 
          <el-table-column
            prop="eachAreaSum"
            class-name="customOne"
            label="每套板件面积"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column> 
          <el-table-column
            prop="totalAreaSum"
            class-name="customOne"
            label="加工总面积"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>  
          <el-table-column
            prop="plankQuantitySalary"
            class-name="customOne"
            label="板材数量"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column> 
          <el-table-column
            prop="plateQuantitySum"
            class-name="customOne"
            label="板件数量"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column> 
          <el-table-column
            prop="eachSheetAreaSum"
            class-name="customOne"
            label="每张板件面积"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column> 
          <el-table-column
            prop="usageRateSum"
            class-name="customOne"
            label="板材利用率"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column> 
          <el-table-column
            prop="pieceWageSum"
            class-name="customOne"
            label="计件工资(线上+线下)"
            min-width="150"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.pieceWageSum |  filterData}}
            </template>
          </el-table-column>
          <el-table-column
            prop="eachPieceWageSum"
            class-name="customOne"
            label="每套计件"
            min-width="150"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.eachPieceWageSum |  filterData}}
            </template>
          </el-table-column>
          <el-table-column
            prop="outWorkWageSum"
            class-name="customOne"
            label="外派工资"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.outWorkWageSum |  filterData}}
            </template>
          </el-table-column>
          <el-table-column
            prop="floatingSalarySum"
            class-name="customOne"
            label="上浮工资"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.floatingSalarySum |  filterData}}
            </template>
          </el-table-column>
          <el-table-column
            prop="applySalarySum"
            class-name="customOne"
            label="增补/借支总额"
            min-width="150"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.applySalarySum |  filterData}}
            </template>
          </el-table-column>
          <el-table-column
            prop="salarySum"
            class-name="customOne"
            label="应发工资总额"
            min-width="150"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.salarySum |  filterData}}
            </template>
          </el-table-column>
          <el-table-column
            prop="actualSalarySum"
            class-name="customOne"
            label="实发工资总额"
            min-width="150"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.actualSalarySum |  filterData}}
            </template>
          </el-table-column>
            <el-table-column
            prop="compensationAmountSum"
            class-name="customOne"
            label="成本赔偿"
            min-width="140"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.compensationAmountSum |  filterData}}
            </template>
          </el-table-column>
          <el-table-column
            prop="punishDeductSum"
            class-name="customOne"
            label="罚款"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.punishDeductSum |  filterData}}
            </template>
          </el-table-column>
 
          <el-table-column
            prop="attendanceRateSum"
            class-name="customOne"
            label="出勤率"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
            <el-table-column
            prop="avgTotalAreaSum"
            class-name="customOne"
            label="人均日加工面积"
            min-width="120"
            align="center"
            show-overflow-tooltip
          > 
          </el-table-column>
          <el-table-column
            prop="avgPlankQuantitySum"
            class-name="customOne"
            label="人均日加工板材数量"
            min-width="120"
            align="center"
            show-overflow-tooltip
          > 
          </el-table-column>
             <el-table-column
            prop="avgPlateQuantitySum"
            class-name="customOne"
            label="人均日加工板件数量"
            min-width="120"
            align="center"
            show-overflow-tooltip
          > 
          </el-table-column>
          <el-table-column
            prop="avgOutputSum"
            class-name="customOne"
            label="人均日产量(套)"
            min-width="120"
            align="center"
            show-overflow-tooltip
          > 
          </el-table-column> 
          <el-table-column
            prop="avgTotalPieceWageSum"
            class-name="customOne"
            label="人均日计件工资"
            min-width="150"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.avgTotalPieceWageSum |  filterData}}
            </template>
          </el-table-column>
          <el-table-column
            prop="avgSalarySum"
            class-name="customOne"
            label="人均日应发工资"
            min-width="150"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.avgSalarySum |  filterData}}
            </template>
          </el-table-column>
          <el-table-column
            prop="avgActualSalarySum"
            class-name="customOne"
            label="人均日实发工资"
            min-width="150"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.avgActualSalarySum |  filterData}}
            </template>
          </el-table-column>
          <el-table-column
            prop="avgCompensationAmountSum"
            class-name="customOne"
            label="人均日成本赔偿"
            min-width="150"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.avgCompensationAmountSum |  filterData}}
            </template>
          </el-table-column>
          <el-table-column
            prop="avgPunishDeductSum"
            class-name="customOne"
            label="人均日罚款"
            min-width="150"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.avgPunishDeductSum |  filterData}}
            </template>
          </el-table-column>
          <el-table-column
            prop="avgPunishDeductSum"
            class-name="customOne"
            label="单张板材应发工资"
            min-width="150"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.avgPunishDeductSum |  filterData}}
            </template>
          </el-table-column>
         <el-table-column
            prop="avgTotalAreaSalarySum"
            class-name="customOne"
            label="每平方板件应发工资成本"
            min-width="200"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.avgTotalAreaSalarySum |  filterData}}
            </template>
          </el-table-column>
            <el-table-column
            prop="avgTotalAreaActualSalarySum"
            class-name="customOne"
            label="每平方板件实发工资成本"
            min-width="200"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.avgTotalAreaActualSalarySum |  filterData}}
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column
          :label="this.filterLastMonth"
          align="center"
          class-name="customOne"
        >
          <el-table-column
            prop="averagePersonPre"
            class-name="customOne"
            min-width="105"
            align="center"
            label="平均人数"
            show-overflow-tooltip
          > 
          </el-table-column>
          <el-table-column
            prop="totalWorkDayPre"
            class-name="customOne"
            label="本厂出勤"
            min-width="120"
            align="center"
            show-overflow-tooltip
          > 
          </el-table-column>
          <el-table-column
            prop="backManDaysPre"
            class-name="customOne"
            label="外厂/部出勤"
            min-width="150"
            align="center"
            show-overflow-tooltip
          > 
          </el-table-column>
          <el-table-column
            prop="totalAttendancePre"
            class-name="customOne"
            label="出勤合计"
            min-width="120"
            align="center"
            show-overflow-tooltip
          > 
          </el-table-column>
          <el-table-column
            prop="totalOutputPre"
            class-name="customOne"
            label="总产量"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column> 
            <el-table-column
            prop="eachAreaPre"
            class-name="customOne"
            label="每套板件面积"
            min-width="150"
            align="center"
            show-overflow-tooltip
          > 
          </el-table-column> 
          <el-table-column
            prop="totalAreaPre"
            class-name="customOne"
            label="加工总面积"
            min-width="120"
            align="center"
            show-overflow-tooltip
          > 
          </el-table-column>  
          <el-table-column
            prop="plankQuantityPre"
            class-name="customOne"
            label="板材数量"
            min-width="120"
            align="center"
            show-overflow-tooltip
          > 
          </el-table-column> 
          <el-table-column
            prop="plateQuantityPre"
            class-name="customOne"
            label="板件数量"
            min-width="120"
            align="center"
            show-overflow-tooltip
          > 
          </el-table-column> 
          <el-table-column
            prop="eachSheetAreaPre"
            class-name="customOne"
            label="每张板件面积"
            min-width="120"
            align="center"
            show-overflow-tooltip
          > 
          </el-table-column> 
          <el-table-column
            prop="usageRatePre"
            class-name="customOne"
            label="板材利用率"
            min-width="120"
            align="center"
            show-overflow-tooltip
          > 
          </el-table-column> 
          <el-table-column
            prop="pieceWagePre"
            class-name="customOne"
            label="计件工资(线上+线下)"
            min-width="150"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.pieceWagePre |  filterData}}
            </template>
          </el-table-column>
          <el-table-column
            prop="eachPieceWagePre"
            class-name="customOne"
            label="每套计件"
            min-width="150"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.eachPieceWagePre |  filterData}}
            </template>
          </el-table-column>
          <el-table-column
            prop="outWorkWagePre"
            class-name="customOne"
            label="外派工资"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.outWorkWagePre |  filterData}}
            </template>
          </el-table-column>
          <el-table-column
            prop="floatingSalaryPre"
            class-name="customOne"
            label="上浮工资"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.floatingSalaryPre |  filterData}}
            </template>
          </el-table-column>
          <el-table-column
            prop="applySalaryPre"
            class-name="customOne"
            label="增补/借支总额"
            min-width="150"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.applySalaryPre |  filterData}}
            </template>
          </el-table-column>
          <el-table-column
            prop="salaryPre"
            class-name="customOne"
            label="应发工资总额"
            min-width="150"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.salaryPre |  filterData}}
            </template>
          </el-table-column>
          <el-table-column
            prop="actualSalaryPre"
            class-name="customOne"
            label="实发工资总额"
            min-width="150"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.actualSalaryPre |  filterData}}
            </template>
          </el-table-column>
            <el-table-column
            prop="compensationAmountPre"
            class-name="customOne"
            label="成本赔偿"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.compensationAmountPre |  filterData}}
            </template>
          </el-table-column>
          <el-table-column
            prop="punishDeductPre"
            class-name="customOne"
            label="罚款"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.punishDeductPre |  filterData}}
            </template>
          </el-table-column> 
          <el-table-column
            prop="attendanceRatePre"
            class-name="customOne"
            label="出勤率"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
            <el-table-column
            prop="avgTotalAreaPre"
            class-name="customOne"
            label="人均日加工面积"
            min-width="150"
            align="center"
            show-overflow-tooltip
          > 
          </el-table-column>
             <el-table-column
            prop="avgPlankQuantityPre"
            class-name="customOne"
            label="人均日加工板材数量"
            min-width="200"
            align="center"
            show-overflow-tooltip
          > 
          </el-table-column>
              <el-table-column
            prop="avgPlateQuantityPre"
            class-name="customOne"
            label="人均日加工板件数量"
            min-width="200"
            align="center"
            show-overflow-tooltip
          > 
          </el-table-column>
          <el-table-column
            prop="avgOutputPre"
            class-name="customOne"
            label="人均日产量(套)"
            min-width="150"
            align="center"
            show-overflow-tooltip
          > 
          </el-table-column> 
          <el-table-column
            prop="avgPieceWagePre"
            class-name="customOne"
            label="人均日计件工资"
            min-width="150"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.avgPieceWagePre |  filterData}}
            </template>
          </el-table-column>
          <el-table-column
            prop="avgSalaryPre"
            class-name="customOne"
            label="人均日应发工资"
            min-width="150"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.avgSalaryPre |  filterData}}
            </template>
          </el-table-column>
          <el-table-column
            prop="avgActualSalaryPre"
            class-name="customOne"
            label="人均日实发工资"
            min-width="150"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.avgActualSalaryPre |  filterData}}
            </template>
          </el-table-column>
          <el-table-column
            prop="avgCompensationAmountPre"
            class-name="customOne"
            label="人均日成本赔偿"
            min-width="150"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.avgCompensationAmountPre |  filterData}}
            </template>
          </el-table-column>
          <el-table-column
            prop="avgPunishDeductPre"
            class-name="customOne"
            label="人均日罚款"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.avgPunishDeductPre |  filterData}}
            </template>
          </el-table-column>
            <el-table-column
            prop="plankQuantitySalaryPre"
            class-name="customOne"
            label="单张板材应发工资"
            min-width="150"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.plankQuantitySalaryPre |  filterData}}
            </template>
          </el-table-column>
              <el-table-column
            prop="avgTotalAreaSalaryPre"
            class-name="customOne"
            label="每平方板件应发工资成本"
            min-width="200"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.avgTotalAreaSalaryPre |  filterData}}
            </template>
          </el-table-column>
                <el-table-column
            prop="avgTotalAreaActualSalaryPre"
            class-name="customOne"
            label="每平方板件实发工资成本"
            min-width="200"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.avgTotalAreaActualSalaryPre |  filterData}}
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column
          :label="this.filterMonth"
          align="center"
          class-name="customOne"
        >
          <el-table-column
            prop="averagePerson"
            class-name="customOne"
            min-width="105"
            align="center"
            label="平均人数"
            show-overflow-tooltip
          > 
          </el-table-column>
          <el-table-column
            prop="totalWorkDay"
            class-name="customOne"
            label="本厂出勤"
            min-width="120"
            align="center"
            show-overflow-tooltip
          > 
          </el-table-column>
          <el-table-column
            prop="backManDays"
            class-name="customOne"
            label="外厂/部出勤"
            min-width="120"
            align="center"
            show-overflow-tooltip
          > 
          </el-table-column>
          <el-table-column
            prop="totalAttendance"
            class-name="customOne"
            label="出勤合计"
            min-width="120"
            align="center"
            show-overflow-tooltip
          > 
          </el-table-column>
          <el-table-column
            prop="totalOutput"
            class-name="customOne"
            label="总产量"
            min-width="120"
            align="center"
            show-overflow-tooltip
          > 
          </el-table-column> 
           <el-table-column
            prop="eachArea"
            class-name="customOne"
            label="每套板件面积"
            min-width="150"
            align="center"
            show-overflow-tooltip
          > 
          </el-table-column> 
          <el-table-column
            prop="totalArea"
            class-name="customOne"
            label="加工总面积"
            min-width="150"
            align="center"
            show-overflow-tooltip
          > 
          </el-table-column>
            <el-table-column
            prop="plankQuantity"
            class-name="customOne"
            label="板材数量"
            min-width="120"
            align="center"
            show-overflow-tooltip
          > 
          </el-table-column>
          <el-table-column
            prop="plateQuantity"
            class-name="customOne"
            label="板件数量"
            min-width="120"
            align="center"
            show-overflow-tooltip
          > 
          </el-table-column>
           <el-table-column
            prop="eachSheetArea"
            class-name="customOne"
            label="每张板件面积"
            min-width="150"
            align="center"
            show-overflow-tooltip
          > 
          </el-table-column>
             <el-table-column
            prop="usageRate"
            class-name="customOne"
            label="板材利用率"
            min-width="120"
            align="center"
            show-overflow-tooltip
          > 
          </el-table-column>
          <el-table-column
            prop="pieceWage"
            class-name="customOne"
            label="计件工资(线上+线下)"
            min-width="150"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.pieceWage |  filterData}}
            </template>
          </el-table-column>
          <el-table-column
            prop="eachPieceWage"
            class-name="customOne"
            label="每套计件"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.eachPieceWage |  filterData}}
            </template>
          </el-table-column>
          <el-table-column
            prop="outWorkWage"
            class-name="customOne"
            label="外派工资"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.outWorkWage |  filterData}}
            </template>
          </el-table-column>
          <el-table-column
            prop="floatingSalary"
            class-name="customOne"
            label="上浮工资"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.floatingSalary |  filterData}}
            </template>
          </el-table-column>
          <el-table-column
            prop="applySalary"
            class-name="customOne"
            label="增补/借支总额"
            min-width="150"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.applySalary |  filterData}}
            </template>
          </el-table-column>
          <el-table-column
            prop="salary"
            class-name="customOne"
            label="应发工资总额"
            min-width="150"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.salary |  filterData}}
            </template>
          </el-table-column>
          <el-table-column
            prop="actualSalary"
            class-name="customOne"
            label="实发工资总额"
            min-width="150"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.actualSalary |  filterData}}
            </template>
          </el-table-column>
        <el-table-column
            prop="compensationAmount"
            class-name="customOne"
            label="成本赔偿"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.compensationAmount |  filterData}}
            </template>
          </el-table-column>
          <el-table-column
            prop="punishDeduct"
            class-name="customOne"
            label="罚款"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.punishDeduct |  filterData}}
            </template>
          </el-table-column>
          <el-table-column
            prop="attendanceRate"
            class-name="customOne"
            label="出勤率"
            min-width="120"
            align="center"
            show-overflow-tooltip
          > 
          </el-table-column> 
         <el-table-column
            prop="avgTotalArea"
            class-name="customOne"
            label="人均日加工面积"
            min-width="200"
            align="center"
            show-overflow-tooltip
          > 
          </el-table-column> 
          <el-table-column
            prop="avgPlankQuantity"
            class-name="customOne"
            label="人均日加工板材数量"
            min-width="200"
            align="center"
            show-overflow-tooltip
          > 
          </el-table-column> 
          <el-table-column
            prop="avgPlateQuantity"
            class-name="customOne"
            label="人均日加工板件数量"
            min-width="200"
            align="center"
            show-overflow-tooltip
          > 
          </el-table-column> 
        
          <el-table-column
            prop="avgOutput"
            class-name="customOne"
            label="人均日产量(套)"
            min-width="200"
            align="center"
            show-overflow-tooltip
          > 
          </el-table-column> 
          <el-table-column
            prop="avgPieceWage"
            class-name="customOne"
            label="人均日计件工资"
            min-width="200"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.avgPieceWage |  filterData}}
            </template>
          </el-table-column>
          <el-table-column
            prop="avgSalary"
            class-name="customOne"
            label="人均日应发工资"
            min-width="200"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.avgSalary |  filterData}}
            </template>
          </el-table-column>
          <el-table-column
            prop="avgActualSalary"
            class-name="customOne"
            label="人均日实发工资"
            min-width="200"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.avgActualSalary |  filterData}}
            </template>
          </el-table-column>
              <el-table-column
            prop="avgCompensationAmount"
            class-name="customOne"
            label="人均日成本赔偿"
            min-width="200"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.avgCompensationAmount |  filterData}}
            </template>
          </el-table-column>
          <el-table-column
            prop="avgPunishDeduct"
            class-name="customOne"
            label="人均日罚款"
            min-width="200"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.avgPunishDeduct |  filterData}}
            </template>
          </el-table-column>
                    <el-table-column
            prop="plankQuantitySalary"
            class-name="customOne"
            label="单张板材应发工资"
            min-width="200"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.plankQuantitySalary |  filterData}}
            </template>
          </el-table-column>
                    <el-table-column
            prop="avgTotalAreaSalary"
            class-name="customOne"
            label="每平方板件应发工资成本"
            min-width="200"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.avgTotalAreaSalary |  filterData}}
            </template>
          </el-table-column>
          <el-table-column
            prop="avgTotalAreaActualSalary"
            class-name="customOne"
            label="每平方板件实发工资成本"
            min-width="200"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.avgTotalAreaActualSalary |  filterData}}
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column
          :label="this.filterContrast"
          align="center"
          class-name="customOne"
        >
          <el-table-column
            prop="peopleChangeRate"
            class-name="customOne"
            label="人数变动"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="workDayChangeRate"
            class-name="customOne"
            label="内部出勤变动"
            min-width="150"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="backManDayChangeRate"
            class-name="customOne"
            label="外部出勤变动"
            min-width="150"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="totalAttendanceChangeRate"
            class-name="customOne"
            label="总出勤变动"
            min-width="150"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="eachTotalPieceWageChangeRate"
            class-name="customOne"
            label="单套计件变动"
            min-width="150"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="attendanceRateChangeRate"
            class-name="customOne"
            label="出勤率变动"
            min-width="150"
            align="center"
            show-overflow-tooltip
          >
        </el-table-column>
          <el-table-column
            prop="avgTotalAreaChangeRate"
            class-name="customOne"
            label="人均日加工面积变动"
            min-width="200"
            align="center"
            show-overflow-tooltip
          >
           <template slot="header">
            <span>人均日加工面积变动</span>
            <el-tooltip>
              <div slot="content">
               (本月人均日加工面积-上月人均日加工面积)/上月人均日加工面积
              </div>
              <i class="el-icon-question"></i>
            </el-tooltip>
          </template>
          </el-table-column>
                       <el-table-column
            prop="avgPlankQuantityChangeRate"
            class-name="customOne"
            label="人均日加工板材数量变动"
            min-width="200"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
                    <el-table-column
            prop="avgPlateQuantityChangeRate"
            class-name="customOne"
            label="人均日加工板件数量变动"
            min-width="200"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="avgOutputChangeRate"
            class-name="customOne"
            label="日均产量变动"
            min-width="150"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="avgPieceWageChangeRate"
            class-name="customOne"
            label="日均计件变动"
            min-width="150"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="avgTotalPieceWageChangeRate"
            class-name="customOne"
            label="日均计件变动"
            min-width="150"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="avgSalaryChangeRate"
            class-name="customOne"
            label="日均应发变动"
            min-width="150"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="avgActualSalaryChangeRate"
            class-name="customOne"
            label="日均实发变动"
            min-width="150"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
            <el-table-column
            prop="avgCompensationAmountChangeRate"
            class-name="customOne"
            label="人均日成本赔偿变动"
            min-width="200"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="avgPunishDeductChangeRate"
            class-name="customOne"
            label="日均罚款变动"
            min-width="150"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
     <el-table-column
            prop="plankQuantitySalaryChangeRate"
            class-name="customOne"
            label="单张板材应发工资变动"
            min-width="200"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
               <el-table-column
            prop="avgTotalAreaSalaryChangeRate"
            class-name="customOne"
            label="每平方板件应发工资成本变动"
            min-width="200"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
               <el-table-column
            prop="avgTotalAreaActualSalaryChangeRate"
            class-name="customOne"
            label="每平方板件实发工资成本变动"
            min-width="200"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
           <el-table-column
            prop="usageRateChangeRate"
            class-name="customOne"
            label="板材利用率变动"
            min-width="200"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
        </el-table-column>  
      </el-table>
    </table-panel>
  </content-panel>
</template>
<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";

import { moneyFormat, calculateTableWidth } from "@/utils";
import moment from "moment";
export default {
  name: "customizedLaborEfficiency",
  mixins: [tableMixin, pagePathMixin],
  data() {
    return {
      searchForm: {
        factoryId: "",
        accountingMonth: moment().subtract(1, "months").format("YYYY-MM"),
        laborEffectId: "",
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      tabList: [],
      bigProcessList:[],
      tableData: [], //表格数据
      loading: false,
      filterParam: {},
      //表格表头信息
      tableHeader: [],
      resizeOffset: 45,
      spanMap: {},
      spanIndex: 0,
      spanIndex2: 0,
    };
  },
  async created() {
    await this.getFactory(); 
    await this.getManualEfficiencyProcessList(); 
    this.onSearch();
  },
  computed: { 
filterMonth() {
      let accountingMonth = this.searchForm.accountingMonth
        ? this.searchForm.accountingMonth
        : moment().subtract(1, "months").startOf("month");
      return `${moment(accountingMonth).format("YYYY年MM月")}`;
    },
    filterAmountto() {
      let accountingMonth = this.searchForm.accountingMonth
        ? this.searchForm.accountingMonth
        : moment().subtract(1, "months").startOf("month");
      return `${moment(accountingMonth).format("YYYY年")}01-12月合计`;
    },
    filterLastMonth() {
      let currentDate = new Date(this.searchForm.accountingMonth);
      let year = currentDate.getFullYear();
      let month = currentDate.getMonth() + 1;
      let lastMonthYear = month === 1 ? year - 1 : year;
      let lastMonthMonth = month === 1 ? 12 : month - 1;
      return `${lastMonthYear}年${String(lastMonthMonth).padStart(2, '0')}月`;
    },
    filterContrast(){
      let currentDate = new Date(this.searchForm.accountingMonth);
      let year = currentDate.getFullYear();
      let month = currentDate.getMonth() + 1;
      let lastMonthYear = month === 1 ? year - 1 : year;
      let lastMonthMonth = month === 1 ? 12 : month - 1;
      let text = `${this.filterMonth}与${String(lastMonthMonth).padStart(2, '0')}月对比`;
      let text2 = `${this.filterMonth}与${this.filterLastMonth}对比`;
      return year == lastMonthYear ? text : text2;
    },
    //  filterLastMonth() {
    //   let accountingMonth = this.searchForm.accountingMonth;
    //   let currentDate = new Date(accountingMonth); 
    //   let currentMonthYear = currentDate.getFullYear();
    //   let currentMonthMonth = String(currentDate.getMonth() + 1).padStart(2, '0');

    //   return `${currentMonthYear}年${currentMonthMonth}月`;
    // }
  },
    filters: {
    filterData(value) {
      return !value ? "-" : moneyFormat(value);
    },
  },
  methods: {
    //获取工厂
    getFactory() {
      return this.$api.systemManage.getBasicPermission
        .getBasicPermissionAll()
        .then(({ data }) => {
          this.tabList = data || [];
        });
    },
    //获取人效工序
     getManualEfficiencyProcessList() {
      this.loading = true;
      let params = {
        pageNum: 1,
        pageSize: 100,
        filterData: {
         
        },
      };
      this.$api.reportManagement
        .getLaborEfficiencyList(params)
        .then((res) => {
          this.bigProcessList = res.data.list; 
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //获取工资分析表
    getList() {
      this.loading = true;
      this.$api.reportManagement.laborEfficiency
        .getlaborEfficiencyList({ ...this.filterParam })
        .then( (res) => {
            this.tableData = res.data;
            this.computeSpanData(res.data);
          }
        )
        .finally(() => {
          this.loading = false;
        });
    },
    // 遍历表格数据，计算合并行数据
    computeSpanData(list) {
      this.spanMap = {};
      list.forEach((item, index) => {
        this.recordSpanData(index, list);
      });
    },
      // 计算每一行的合并行数据
    recordSpanData(index, data) {
      if (index === 0) {
        this.spanMap[index] = {
          level1: 1,
          level2: 1,
        };
        this.spanIndex = 0;
        this.spanIndex2 = 0;
      } else {
        // 一级合并
        if (data[index].factoryName === data[index - 1].factoryName) {
          let spanRow = this.spanMap[this.spanIndex];
          spanRow.level1 += 1;
          // 二级合并
          let level2 = 1;
          if (
            data[index].factoryName === data[index - 1].factoryName
          ) {
            spanRow = this.spanMap[this.spanIndex2];
            spanRow.level2 += 1;
            // 当前行参与二级合并，当前行level2 = 0
            level2 = 0;
          } else {
            this.spanIndex2 = index;
          }
          this.spanMap[index] = {
            level1: 0,
            level2,
          };
        } else {
          this.spanMap[index] = {
            level1: 1,
            level2: 1,
          };
          this.spanIndex = index;
          this.spanIndex2 = index;
        }
      }
    },
    objectSpanMethod({row, column, rowIndex, columnIndex }) {
      const spanRow = this.spanMap[rowIndex];
      if (!spanRow) {
        return;
      }

      if (columnIndex == 0) {
        return {
          rowspan: spanRow.level1,
          colspan: 1
        };
      }
    }, 
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.filterParam = {};
      this.searchForm.factoryId = ""
      this.onSearch();
    },
    //搜索
    onSearch() {
      let month = this.searchForm.accountingMonth;
      this.searchForm.accountingMonth = month ? month : moment().subtract(1, "months").format("YYYY-MM");
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (key == "accountingMonth" && val) {
          this.filterParam.accountingMonth = moment(val).format("YYYY-MM");
        } else if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      this.pageNum = 1;
      this.getList();
    },
    //计算表格列宽度
    flexWidth(...args) {
      return calculateTableWidth(...args);
    },
    //导出全部
    handleExport() {
      let exportForm = JSON.parse(JSON.stringify(this.filterParam));
      let params = {
        ...exportForm
      };
      this.$api.reportManagement.laborEfficiency.exportLaborEfficiencyList(params).then((res) => {
        this.$message.success("导出操作成功，请前往导出记录查看详情");
      });
    },

  },
};
</script>
<style lang="stylus" scoped>
ellipsis() {
  font-size: 16px;
  font-weight: 600;
}

.header_tableName {
  ellipsis();
  font-size: 22px;
  text-align: center;
  margin-bottom: 8px;
}

>>>.el-table__cell {
  .cell {
    width: 100% !important;
    padding: 0 8px !important;
  }
}

>>>.el-table__fixed {
  height: auto !important;
  bottom: 17px !important;
}
</style>
